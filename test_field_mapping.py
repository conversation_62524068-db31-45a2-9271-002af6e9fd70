#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段映射功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.xianyu_product import XianyuProduct
from config.database import get_database_config
from loguru import logger

async def test_field_mapping():
    """测试字段映射功能"""
    logger.info("🧪 开始测试字段映射功能")
    
    # 获取数据库配置
    config = get_database_config()
    db_config = {
        'host': config['host'],
        'port': config['port'],
        'user': config['user'],
        'password': config['password'],
        'database': config['db']
    }
    
    # 创建数据模型
    product_model = XianyuProduct(db_config)
    
    # 测试数据1：包含 detail_desc 字段
    test_product_1 = {
        'item_id': 'test_mapping_1',
        'title': '原始标题',
        'detail_title': '详情标题（优先）',
        'description': '原始描述',
        'detail_desc': '详情描述（优先）',  # 这个会被保存到 description 字段
        'sold_price': '10.00',
        'seller_id': 'test_seller_mapping',
        'seller_nick': '测试卖家',
        'gmt_create': 1640995200000,
        'gmt_create_date': '2022-01-01 00:00:00'
    }
    
    # 测试数据2：只有 description 字段
    test_product_2 = {
        'item_id': 'test_mapping_2',
        'title': '标题2',
        'description': '只有原始描述',  # 没有 detail_desc，会使用这个
        'sold_price': '20.00',
        'seller_id': 'test_seller_mapping',
        'seller_nick': '测试卖家',
        'gmt_create': 1640995200000,
        'gmt_create_date': '2022-01-01 00:00:00'
    }
    
    # 测试数据3：只有 desc 字段
    test_product_3 = {
        'item_id': 'test_mapping_3',
        'title': '标题3',
        'desc': '简短描述',  # 会被映射到 description 字段
        'sold_price': '30.00',
        'seller_id': 'test_seller_mapping',
        'seller_nick': '测试卖家',
        'gmt_create': 1640995200000,
        'gmt_create_date': '2022-01-01 00:00:00'
    }
    
    try:
        # 保存测试数据
        logger.info("📝 测试数据1：包含 detail_desc（优先级最高）")
        success1 = await product_model.save_product(test_product_1)
        logger.info(f"   保存结果: {'✅ 成功' if success1 else '❌ 失败'}")
        
        logger.info("📝 测试数据2：只有 description")
        success2 = await product_model.save_product(test_product_2)
        logger.info(f"   保存结果: {'✅ 成功' if success2 else '❌ 失败'}")
        
        logger.info("📝 测试数据3：只有 desc")
        success3 = await product_model.save_product(test_product_3)
        logger.info(f"   保存结果: {'✅ 成功' if success3 else '❌ 失败'}")
        
        if success1 and success2 and success3:
            logger.info("🎉 字段映射测试全部通过！")
            logger.info("📋 映射规则说明:")
            logger.info("   - description 字段映射优先级: detail_desc > desc > description")
            logger.info("   - title 字段映射优先级: detail_title > title")
            logger.info("   - sold_price 字段映射优先级: sold_price > price")
        else:
            logger.error("❌ 部分字段映射测试失败")
        
        # 关闭连接池
        await product_model.close_db_pool()
        
    except Exception as e:
        logger.error(f"❌ 字段映射测试异常: {e}")

def show_mapping_rules():
    """显示当前的字段映射规则"""
    logger.info("📋 当前字段映射规则:")
    logger.info("   数据库字段 <- product_data字段（优先级从左到右）")
    logger.info("   description <- ['detail_desc', 'desc', 'description']")
    logger.info("   title <- ['detail_title', 'title']")
    logger.info("   sold_price <- ['sold_price', 'price']")
    logger.info("")
    logger.info("💡 使用示例:")
    logger.info("   如果 product_data 包含 'detail_desc'，会保存到数据库的 'description' 字段")
    logger.info("   如果没有 'detail_desc' 但有 'desc'，会使用 'desc' 的值")
    logger.info("   如果都没有，最后使用 'description' 的值")

async def main():
    """主测试函数"""
    logger.info("🚀 开始字段映射测试")
    
    # 显示映射规则
    show_mapping_rules()
    
    # 运行测试
    await test_field_mapping()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
