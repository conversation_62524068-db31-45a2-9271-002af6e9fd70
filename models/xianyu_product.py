#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼商品数据模型
"""

import json
import asyncio
import aiomysql
from datetime import datetime
from loguru import logger
from typing import Dict, List, Optional


class XianyuProduct:
    """闲鱼商品数据模型"""
    
    def __init__(self, db_config: dict):
        """
        初始化商品模型
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.pool = None
    
    async def init_db_pool(self):
        """初始化数据库连接池"""
        try:
            # 适配不同的数据库配置格式
            db_name = self.db_config.get('database') or self.db_config.get('db')

            self.pool = await aiomysql.create_pool(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                db=db_name,
                charset='utf8mb4',
                autocommit=True,
                maxsize=10,
                minsize=1
            )
            logger.info("✅ 数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"❌ 数据库连接池初始化失败: {e}")
            raise
    
    async def close_db_pool(self):
        """关闭数据库连接池"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            logger.info("✅ 数据库连接池已关闭")
    
    async def save_product(self, product_data: dict) -> bool:
        """
        保存单个商品数据到数据库
        
        Args:
            product_data: 商品数据字典
            
        Returns:
            bool: 保存是否成功
        """
        if not self.pool:
            await self.init_db_pool()
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 构建插入SQL
                    sql = """
                    INSERT INTO xianyu_products (
                        -- 商品基本信息
                        item_id, title, description, sold_price, original_price, quantity,
                        item_status, item_status_str,
                        -- 商品统计数据
                        browse_cnt, want_cnt, favor_cnt, collect_cnt,
                        -- 商品分类信息
                        category_id, cat_id, channel_cat_id, root_channel_cat_id,
                        level2_channel_cat_id, level3_channel_cat_id,
                        -- 商品促销信息
                        promotion_name, promotion_desc, promotion_price_min, min_discount_fee,
                        -- 商品图片信息
                        main_image_url, image_count, all_image_urls,
                        -- 商品标签信息
                        item_label_text, item_label_channel_cate_id,
                        -- 商品分类名称
                        good_name, bucket, user_num,
                        -- 卖家基本信息
                        seller_id, seller_nick, seller_unique_name, seller_city, 
                        seller_signature, seller_portrait_url,
                        -- 卖家统计信息
                        seller_item_count, seller_has_sold_num, seller_user_reg_day, 
                        seller_register_time, seller_last_visit_time,
                        -- 卖家信用信息
                        seller_zhima_auth, seller_zhima_level_code, seller_zhima_level_name,
                        seller_reply_ratio_24h, seller_reply_interval, seller_reply_ratio_double, 
                        seller_avg_reply_30d,
                        -- 卖家评价统计
                        seller_good_remark_cnt, seller_bad_remark_cnt, seller_default_remark_cnt,
                        -- 卖家信用标签
                        seller_level,
                        -- 追踪参数
                        seller_bucket_id, idle_label_bucket_id,
                        -- 时间信息
                        gmt_create, gmt_create_date
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s,
                        %s, %s, %s,
                        %s, %s,
                        %s, %s, %s,
                        %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s,
                        %s,
                        %s, %s,
                        %s, %s
                    ) ON DUPLICATE KEY UPDATE
                        -- 更新商品信息
                        title = VALUES(title),
                        description = VALUES(description),
                        sold_price = VALUES(sold_price),
                        original_price = VALUES(original_price),
                        quantity = VALUES(quantity),
                        item_status = VALUES(item_status),
                        item_status_str = VALUES(item_status_str),
                        browse_cnt = VALUES(browse_cnt),
                        want_cnt = VALUES(want_cnt),
                        favor_cnt = VALUES(favor_cnt),
                        collect_cnt = VALUES(collect_cnt),
                        -- 更新卖家信息
                        seller_nick = VALUES(seller_nick),
                        seller_unique_name = VALUES(seller_unique_name),
                        seller_city = VALUES(seller_city),
                        seller_signature = VALUES(seller_signature),
                        seller_portrait_url = VALUES(seller_portrait_url),
                        seller_item_count = VALUES(seller_item_count),
                        seller_has_sold_num = VALUES(seller_has_sold_num),
                        seller_last_visit_time = VALUES(seller_last_visit_time),
                        seller_reply_ratio_24h = VALUES(seller_reply_ratio_24h),
                        seller_reply_interval = VALUES(seller_reply_interval),
                        seller_reply_ratio_double = VALUES(seller_reply_ratio_double),
                        seller_avg_reply_30d = VALUES(seller_avg_reply_30d),
                        seller_good_remark_cnt = VALUES(seller_good_remark_cnt),
                        seller_bad_remark_cnt = VALUES(seller_bad_remark_cnt),
                        seller_default_remark_cnt = VALUES(seller_default_remark_cnt),
                        updated_at = CURRENT_TIMESTAMP
                    """
                    
                    # 准备数据
                    values = self._prepare_product_values(product_data)
                    
                    # 执行插入
                    await cursor.execute(sql, values)
                    
                    logger.debug(f"✅ 商品数据保存成功: {product_data.get('desc', '未知')}")
                    return True
                    
        except Exception as e:
            logger.error(f"❌ 保存商品数据失败: {product_data.get('item_id', '未知')} - {e}")
            return False
    
    def _prepare_product_values(self, product_data: dict) -> tuple:
        """
        准备插入数据库的值

        Args:
            product_data: 商品数据字典

        Returns:
            tuple: 准备好的数据元组
        """
        # 字段映射规则：定义 product_data 字段到数据库字段的映射
        field_mapping = {
            'description': ['detail_desc', 'desc', 'description'],  # 优先级从左到右
            'title': ['detail_title', 'title'],
            'sold_price': ['sold_price', 'price'],
            # 可以继续添加其他字段映射
        }

        def get_mapped_value(field_name: str, default=''):
            """根据映射规则获取字段值"""
            if field_name in field_mapping:
                for source_field in field_mapping[field_name]:
                    value = product_data.get(source_field)
                    if value is not None and value != '':
                        return value
                return default
            else:
                return product_data.get(field_name, default)

        # 处理图片URL数组
        all_image_urls = product_data.get('all_image_urls', [])
        if isinstance(all_image_urls, list):
            all_image_urls_json = json.dumps(all_image_urls, ensure_ascii=False)
        else:
            all_image_urls_json = '[]'

        logger.info(product_data)
        return (
            # 商品基本信息
            get_mapped_value('item_id'),
            get_mapped_value('title'),
            get_mapped_value('description'),  # 会按照映射规则优先使用 detail_desc
            float(get_mapped_value('sold_price') or 0),
            float(product_data.get('original_price', 0) or 0),
            int(product_data.get('quantity', 0) or 0),
            int(product_data.get('item_status', 0) or 0),
            product_data.get('item_status_str', ''),
            
            # 商品统计数据
            int(product_data.get('browse_cnt', 0) or 0),
            int(product_data.get('want_cnt', 0) or 0),
            int(product_data.get('favor_cnt', 0) or 0),
            int(product_data.get('collect_cnt', 0) or 0),
            
            # 商品分类信息
            int(product_data.get('category_id', 0) or 0),
            int(product_data.get('cat_id', 0) or 0),
            int(product_data.get('channel_cat_id', 0) or 0),
            int(product_data.get('root_channel_cat_id', 0) or 0),
            int(product_data.get('level2_channel_cat_id', 0) or 0),
            int(product_data.get('level3_channel_cat_id', 0) or 0),
            
            # 商品促销信息
            product_data.get('promotion_name', ''),
            product_data.get('promotion_desc', ''),
            float(product_data.get('promotion_price_min', 0) or 0),
            float(product_data.get('min_discount_fee_str', 0) or 0),
            
            # 商品图片信息
            product_data.get('main_image_url', ''),
            int(product_data.get('image_count', 0) or 0),
            all_image_urls_json,
            
            # 商品标签信息
            product_data.get('item_label_text', ''),
            int(product_data.get('item_label_channel_cate_id', 0) or 0),
            
            # 商品分类名称
            product_data.get('good_name', ''),
            product_data.get('bucket', ''),
            product_data.get('user_num', ''),
            
            # 卖家基本信息
            product_data.get('seller_id', ''),
            product_data.get('seller_nick', ''),
            product_data.get('seller_unique_name', ''),
            product_data.get('seller_city', ''),
            product_data.get('seller_signature', ''),
            product_data.get('seller_portrait_url', ''),
            
            # 卖家统计信息
            int(product_data.get('seller_item_count', 0) or 0),
            int(product_data.get('seller_has_sold_num', 0) or 0),
            int(product_data.get('seller_user_reg_day', 0) or 0),
            int(product_data.get('seller_register_time', 0) or 0),
            product_data.get('seller_last_visit_time', ''),
            
            # 卖家信用信息
            int(product_data.get('seller_zhima_auth', 0) or 0),
            product_data.get('seller_zhima_level_code', ''),
            product_data.get('seller_zhima_level_name', ''),
            product_data.get('seller_reply_ratio_24h', ''),
            product_data.get('seller_reply_interval', ''),
            float(product_data.get('seller_reply_ratio_double', 0) or 0),
            int(product_data.get('seller_avg_reply_30d', 0) or 0),
            
            # 卖家评价统计
            int(product_data.get('seller_good_remark_cnt', 0) or 0),
            int(product_data.get('seller_bad_remark_cnt', 0) or 0),
            int(product_data.get('seller_default_remark_cnt', 0) or 0),
            
            # 卖家信用标签
            product_data.get('seller_level', ''),
            
            # 追踪参数
            product_data.get('seller_bucket_id', ''),
            product_data.get('idle_label_bucket_id', ''),
            
            # 时间信息
            int(product_data.get('gmt_create', 0) or 0),
            product_data.get('gmt_create_date', '')
        )
    
    async def save_products_batch(self, products_data: List[dict]) -> Dict[str, int]:
        """
        批量保存商品数据
        
        Args:
            products_data: 商品数据列表
            
        Returns:
            dict: 保存结果统计
        """
        if not products_data:
            return {'success': 0, 'failed': 0, 'total': 0}
        
        success_count = 0
        failed_count = 0
        
        logger.info(f"🚀 开始批量保存商品数据，共{len(products_data)}个商品")
        
        for i, product_data in enumerate(products_data, 1):
            try:
                success = await self.save_product(product_data)
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                
                # 每10个商品输出一次进度
                if i % 10 == 0:
                    logger.info(f"📊 批量保存进度: {i}/{len(products_data)} (成功:{success_count}, 失败:{failed_count})")
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"❌ 批量保存商品异常: {product_data.get('item_id', '未知')} - {e}")
        
        result = {
            'success': success_count,
            'failed': failed_count,
            'total': len(products_data)
        }
        
        logger.info(f"🎉 批量保存完成: 成功{success_count}个, 失败{failed_count}个, 总计{len(products_data)}个")
        return result
