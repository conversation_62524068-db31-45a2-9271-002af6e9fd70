#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼用户数据模型
提供用户数据的CRUD操作和数据转换功能

Author: AI Assistant
Date: 2025-07-30
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger

from db.mysql_manager import get_db_manager


class XianyuUser:
    """闲鱼用户数据模型"""
    
    def __init__(self, data: Optional[Dict[str, Any]] = None):
        """
        初始化用户数据模型
        
        Args:
            data: 用户数据字典
        """
        self.data = data or {}
    
    @classmethod
    def from_api_response(cls, api_data: Dict[str, Any]) -> 'XianyuUser':
        """
        从API响应数据创建用户模型
        
        Args:
            api_data: API响应的原始数据
            
        Returns:
            XianyuUser: 用户模型实例
        """
        user = cls()
        user._parse_api_data(api_data)
        return user
    
    def _parse_api_data(self, api_data: Dict[str, Any]):
        """
        解析API数据并填充到模型中
        
        Args:
            api_data: API响应数据
        """
        try:
            data = api_data.get('data', {})
            
            # 基础信息
            base_info = data.get('baseInfo', {})
            self.data.update({
                'user_id': base_info.get('kcUserId', ''),
                'encrypted_user_id': base_info.get('encryptedUserId', ''),
                'user_type': base_info.get('userType', 1),
                'is_self': base_info.get('self', False),
                'ios_verify': base_info.get('iosVerify', False),
            })
            
            # 用户标签
            tags = base_info.get('tags', {})
            self.data.update({
                'real_name_certified': tags.get('real_name_certification_77', False),
                'xianyu_user_upgrade': tags.get('xianyu_user_upgrade', False),
                'zhima_certified': tags.get('idle_zhima_zheng', False),
                'tb_xianyu_user': tags.get('tb_xianyu_user', False),
                'alibaba_idle_playboy': tags.get('alibaba_idle_playboy', False),
            })
            
            # 模块信息
            module = data.get('module', {})
            
            # 店铺信息
            shop = module.get('shop', {})
            self.data.update({
                'shop_level': shop.get('level', ''),
                'shop_score': shop.get('score', 0),
                'next_level_need_score': shop.get('nextLevelNeedScore', 0),
                'praise_ratio': shop.get('praiseRatio', 0.0),
                'review_num': shop.get('reviewNum', 0),
                'item_topping_limit': shop.get('itemToppingLimit', 0),
                'show_phone_number': shop.get('showPhoneNumber', False),
                'super_show': shop.get('superShow', False),
                'level_jump_url': shop.get('levelJumpUrl', ''),
            })
            
            # 社交信息
            social = module.get('social', {})
            self.data.update({
                'follow_status': social.get('followStatus', 0),
                'followers_count': int(social.get('followers', '0')),
                'following_count': int(social.get('following', '0')),
                'attention_privacy_protected': social.get('attentionPrivacyProtected', 'false') == 'true',
            })
            
            # 标签页信息
            tabs = module.get('tabs', {})
            item_tab = tabs.get('item', {})
            rate_tab = tabs.get('rate', {})
            self.data.update({
                'item_count': item_tab.get('number', 0),
                'rate_count': int(rate_tab.get('number', '0')),
            })
            
            # 基础展示信息
            base = module.get('base', {})
            self.data.update({
                'display_name': base.get('displayName', ''),
                'ip_location': base.get('ipLocation', ''),
                'avatar_url': base.get('avatar', {}).get('avatar', ''),
            })
            
            # 信用等级信息
            ylz_tags = base.get('ylzTags', [])
            if ylz_tags:
                ylz_tag = ylz_tags[0]  # 取第一个标签
                attributes = ylz_tag.get('attributes', {})
                self.data.update({
                    'ylz_buyer_level': attributes.get('level', 0),
                    'ylz_level_text': ylz_tag.get('text', ''),
                    'ylz_level_icon': ylz_tag.get('icon', ''),
                    'ylz_level_lottie': ylz_tag.get('lottie', ''),
                })
            
            # 系统信息
            self.data.update({
                'api_trace_id': api_data.get('traceId', ''),
                'api_version': api_data.get('v', '1.0'),
                'raw_data': json.dumps(api_data, ensure_ascii=False),
            })
            
        except Exception as e:
            logger.error(f"解析API数据失败: {e}")
            raise
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 用户数据字典
        """
        return self.data.copy()
    
    def get_insert_sql(self, allow_duplicate: bool = True) -> tuple[str, tuple]:
        """
        生成插入SQL语句和参数

        Args:
            allow_duplicate: 是否允许重复插入，True=每次都插入新记录，False=去重更新

        Returns:
            tuple: (SQL语句, 参数元组)
        """
        fields = [
            'user_id', 'encrypted_user_id', 'display_name', 'avatar_url', 'user_type', 'ip_location',
            'is_self', 'ios_verify', 'real_name_certified', 'xianyu_user_upgrade', 'zhima_certified',
            'tb_xianyu_user', 'alibaba_idle_playboy', 'shop_level', 'shop_score', 'next_level_need_score',
            'praise_ratio', 'review_num', 'item_topping_limit', 'show_phone_number', 'super_show',
            'level_jump_url', 'follow_status', 'followers_count', 'following_count', 'attention_privacy_protected',
            'item_count', 'rate_count', 'ylz_buyer_level', 'ylz_level_text', 'ylz_level_icon',
            'ylz_level_lottie', 'api_trace_id', 'api_version', 'raw_data'
        ]

        placeholders = ', '.join(['%s'] * len(fields))
        field_names = ', '.join(fields)

        if allow_duplicate:
            # 允许重复插入 - 每次都创建新记录
            sql = f"""
            INSERT INTO xianyu_users ({field_names})
            VALUES ({placeholders})
            """
        else:
            # 去重更新模式 - 遇到重复则更新
            sql = f"""
            INSERT INTO xianyu_users ({field_names})
            VALUES ({placeholders})
            ON DUPLICATE KEY UPDATE
            display_name = VALUES(display_name),
            avatar_url = VALUES(avatar_url),
            ip_location = VALUES(ip_location),
            shop_level = VALUES(shop_level),
            shop_score = VALUES(shop_score),
            next_level_need_score = VALUES(next_level_need_score),
            praise_ratio = VALUES(praise_ratio),
            review_num = VALUES(review_num),
            followers_count = VALUES(followers_count),
            following_count = VALUES(following_count),
            item_count = VALUES(item_count),
            rate_count = VALUES(rate_count),
            raw_data = VALUES(raw_data),
            updated_at = CURRENT_TIMESTAMP
            """

        params = tuple(self.data.get(field, None) for field in fields)
        return sql, params
    
    async def save(self, allow_duplicate: bool = True) -> int:
        """
        保存用户数据到数据库

        Args:
            allow_duplicate: 是否允许重复插入，True=每次都插入新记录，False=去重更新

        Returns:
            int: 插入或更新的记录ID
        """
        if not self.data.get('user_id'):
            raise ValueError("用户ID不能为空")

        db_manager = await get_db_manager()
        sql, params = self.get_insert_sql(allow_duplicate)

        try:
            result_id = await db_manager.execute_insert(sql, params)
            action = "插入新记录" if allow_duplicate else "插入或更新"
            logger.info(f"用户数据{action}成功: {self.data.get('user_id')} ({self.data.get('display_name')}) - 数据库ID: {result_id}")
            return result_id
        except Exception as e:
            logger.error(f"用户数据保存失败: {e}")
            raise
    
    @classmethod
    async def find_by_user_id(cls, user_id: str, latest_only: bool = True) -> Optional['XianyuUser']:
        """
        根据用户ID查找用户

        Args:
            user_id: 用户ID
            latest_only: 是否只返回最新记录，False则返回最早记录

        Returns:
            Optional[XianyuUser]: 用户模型实例或None
        """
        db_manager = await get_db_manager()

        if latest_only:
            sql = "SELECT * FROM xianyu_users WHERE user_id = %s ORDER BY created_at DESC LIMIT 1"
        else:
            sql = "SELECT * FROM xianyu_users WHERE user_id = %s ORDER BY created_at ASC LIMIT 1"

        try:
            results = await db_manager.execute_query(sql, (user_id,))
            if results:
                user = cls()
                user.data = results[0]
                return user
            return None
        except Exception as e:
            logger.error(f"查询用户失败: {e}")
            raise

    @classmethod
    async def find_all_by_user_id(cls, user_id: str, limit: int = 100) -> List['XianyuUser']:
        """
        根据用户ID查找所有历史记录

        Args:
            user_id: 用户ID
            limit: 限制数量

        Returns:
            List[XianyuUser]: 用户历史记录列表（按时间倒序）
        """
        db_manager = await get_db_manager()
        sql = "SELECT * FROM xianyu_users WHERE user_id = %s ORDER BY created_at DESC LIMIT %s"

        try:
            results = await db_manager.execute_query(sql, (user_id, limit))
            users = []
            for row in results:
                user = cls()
                user.data = row
                users.append(user)
            return users
        except Exception as e:
            logger.error(f"查询用户历史记录失败: {e}")
            raise
    
    @classmethod
    async def find_all(cls, limit: int = 100, offset: int = 0) -> List['XianyuUser']:
        """
        查询所有用户
        
        Args:
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[XianyuUser]: 用户列表
        """
        db_manager = await get_db_manager()
        sql = "SELECT * FROM xianyu_users ORDER BY updated_at DESC LIMIT %s OFFSET %s"
        
        try:
            results = await db_manager.execute_query(sql, (limit, offset))
            users = []
            for row in results:
                user = cls()
                user.data = row
                users.append(user)
            return users
        except Exception as e:
            logger.error(f"查询用户列表失败: {e}")
            raise
    
    @classmethod
    async def count_all(cls) -> int:
        """
        统计所有记录总数

        Returns:
            int: 记录总数
        """
        db_manager = await get_db_manager()
        sql = "SELECT COUNT(*) as count FROM xianyu_users"

        try:
            results = await db_manager.execute_query(sql)
            return results[0]['count'] if results else 0
        except Exception as e:
            logger.error(f"统计记录数量失败: {e}")
            raise

    @classmethod
    async def count_unique_users(cls) -> int:
        """
        统计唯一用户数量

        Returns:
            int: 唯一用户数量
        """
        db_manager = await get_db_manager()
        sql = "SELECT COUNT(DISTINCT user_id) as count FROM xianyu_users"

        try:
            results = await db_manager.execute_query(sql)
            return results[0]['count'] if results else 0
        except Exception as e:
            logger.error(f"统计唯一用户数量失败: {e}")
            raise

    @classmethod
    async def get_user_stats(cls, user_id: str) -> Dict[str, Any]:
        """
        获取用户的统计信息

        Args:
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 用户统计信息
        """
        db_manager = await get_db_manager()
        sql = """
        SELECT
            COUNT(*) as total_records,
            MIN(created_at) as first_record,
            MAX(created_at) as latest_record,
            MIN(followers_count) as min_followers,
            MAX(followers_count) as max_followers,
            MIN(shop_score) as min_score,
            MAX(shop_score) as max_score
        FROM xianyu_users
        WHERE user_id = %s
        """

        try:
            results = await db_manager.execute_query(sql, (user_id,))
            if results:
                return results[0]
            return {}
        except Exception as e:
            logger.error(f"获取用户统计信息失败: {e}")
            raise
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"XianyuUser(user_id={self.data.get('user_id')}, name={self.data.get('display_name')})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"XianyuUser({self.data})"
