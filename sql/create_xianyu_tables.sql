-- =============================================
-- 闲鱼商品数据库表结构（单表设计）
-- 创建时间: 2025-07-30
-- 描述: 用于存储闲鱼商品和卖家信息的完整数据表
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 闲鱼商品完整信息表（包含商品和卖家信息）
-- =============================================
DROP TABLE IF EXISTS `xianyu_products`;
CREATE TABLE `xianyu_products` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  -- ========== 商品基本信息 ==========
  `item_id` varchar(50) NOT NULL COMMENT '商品ID',
  `title` text COMMENT '商品标题',
  `description` longtext COMMENT '商品描述',
  `sold_price` decimal(10,2) DEFAULT '0.00' COMMENT '售价',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '原价',
  `quantity` int(11) DEFAULT '0' COMMENT '库存数量',
  `item_status` tinyint(4) DEFAULT '0' COMMENT '商品状态(0:在线)',
  `item_status_str` varchar(20) DEFAULT '' COMMENT '商品状态文字',

  -- ========== 商品统计数据 ==========
  `browse_cnt` int(11) DEFAULT '0' COMMENT '浏览量',
  `want_cnt` int(11) DEFAULT '0' COMMENT '想要人数',
  `favor_cnt` int(11) DEFAULT '0' COMMENT '收藏数',
  `collect_cnt` int(11) DEFAULT '0' COMMENT '收藏数(备用字段)',

  -- ========== 商品分类信息 ==========
  `category_id` bigint(20) DEFAULT '0' COMMENT '分类ID',
  `cat_id` bigint(20) DEFAULT '0' COMMENT '分类ID(备用)',
  `channel_cat_id` bigint(20) DEFAULT '0' COMMENT '渠道分类ID',
  `root_channel_cat_id` bigint(20) DEFAULT '0' COMMENT '根分类ID',
  `level2_channel_cat_id` bigint(20) DEFAULT '0' COMMENT '二级分类ID',
  `level3_channel_cat_id` bigint(20) DEFAULT '0' COMMENT '三级分类ID',

  -- ========== 商品促销信息 ==========
  `promotion_name` varchar(100) DEFAULT '' COMMENT '促销名称',
  `promotion_desc` varchar(200) DEFAULT '' COMMENT '促销描述',
  `promotion_price_min` decimal(10,2) DEFAULT '0.00' COMMENT '促销最低价',
  `min_discount_fee` decimal(10,2) DEFAULT '0.00' COMMENT '最小折扣金额',

  -- ========== 商品图片信息 ==========
  `main_image_url` text COMMENT '主图URL',
  `image_count` tinyint(4) DEFAULT '0' COMMENT '图片数量',
  `all_image_urls` json COMMENT '所有图片URL(JSON格式)',

  -- ========== 商品标签信息 ==========
  `item_label_text` varchar(100) DEFAULT '' COMMENT '商品标签文字',
  `item_label_channel_cate_id` bigint(20) DEFAULT '0' COMMENT '标签分类ID',

  -- ========== 商品分类名称 ==========
  `good_name` varchar(100) DEFAULT '' COMMENT '商品分类名称',
  `bucket` varchar(10) DEFAULT '' COMMENT '分桶信息',
  `user_num` varchar(20) DEFAULT '' COMMENT '用户数量',

  -- ========== 卖家基本信息 ==========
  `seller_id` varchar(50) NOT NULL COMMENT '卖家ID',
  `seller_nick` varchar(100) DEFAULT '' COMMENT '卖家昵称',
  `seller_unique_name` varchar(100) DEFAULT '' COMMENT '卖家唯一名称',
  `seller_city` varchar(50) DEFAULT '' COMMENT '卖家城市',
  `seller_signature` text COMMENT '卖家个性签名',
  `seller_portrait_url` text COMMENT '卖家头像URL',

  -- ========== 卖家统计信息 ==========
  `seller_item_count` int(11) DEFAULT '0' COMMENT '卖家商品数量',
  `seller_has_sold_num` int(11) DEFAULT '0' COMMENT '卖家已售数量',
  `seller_user_reg_day` int(11) DEFAULT '0' COMMENT '卖家注册天数',
  `seller_register_time` bigint(20) DEFAULT '0' COMMENT '卖家注册时间戳',
  `seller_last_visit_time` varchar(50) DEFAULT '' COMMENT '卖家最后访问时间',

  -- ========== 卖家信用信息 ==========
  `seller_zhima_auth` tinyint(1) DEFAULT '0' COMMENT '卖家芝麻认证(0:否,1:是)',
  `seller_zhima_level_code` varchar(10) DEFAULT '' COMMENT '卖家芝麻信用等级代码',
  `seller_zhima_level_name` varchar(50) DEFAULT '' COMMENT '卖家芝麻信用等级名称',
  `seller_reply_ratio_24h` varchar(10) DEFAULT '' COMMENT '卖家24小时回复率',
  `seller_reply_interval` varchar(20) DEFAULT '' COMMENT '卖家平均回复时间',
  `seller_reply_ratio_double` decimal(5,4) DEFAULT '0.0000' COMMENT '卖家24小时回复率(数值)',
  `seller_avg_reply_30d` int(11) DEFAULT '0' COMMENT '卖家30天平均回复时间(分钟)',

  -- ========== 卖家评价统计 ==========
  `seller_good_remark_cnt` int(11) DEFAULT '0' COMMENT '卖家好评数',
  `seller_bad_remark_cnt` int(11) DEFAULT '0' COMMENT '卖家差评数',
  `seller_default_remark_cnt` int(11) DEFAULT '0' COMMENT '卖家默认评价数',

  -- ========== 卖家信用标签 ==========
  `seller_level` varchar(10) DEFAULT '' COMMENT '卖家等级',

  -- ========== 追踪参数 ==========
  `seller_bucket_id` varchar(10) DEFAULT '' COMMENT '卖家分桶ID',
  `idle_label_bucket_id` varchar(10) DEFAULT '' COMMENT '标签分桶ID',

  -- ========== 时间信息 ==========
  `gmt_create` bigint(20) DEFAULT '0' COMMENT '商品创建时间戳',
  `gmt_create_date` varchar(30) DEFAULT '' COMMENT '商品创建时间字符串',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',

  -- ========== 索引设计 ==========
  PRIMARY KEY (`id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_channel_cat_id` (`channel_cat_id`),
  KEY `idx_gmt_create` (`gmt_create`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_seller_status_create` (`seller_id`, `item_status`, `gmt_create`),
  KEY `idx_category_price_browse` (`channel_cat_id`, `sold_price`, `browse_cnt`),
  KEY `idx_popularity` (`browse_cnt`, `want_cnt`, `favor_cnt`),
  FULLTEXT KEY `ft_title_desc` (`title`, `description`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='闲鱼商品完整信息表(包含商品和卖家信息)';

