-- =============================================
-- 闲鱼商品数据库表结构
-- 创建时间: 2025-07-30
-- 描述: 用于存储闲鱼商品和卖家信息
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 商品信息表
-- =============================================
DROP TABLE IF EXISTS `xianyu_products`;
CREATE TABLE `xianyu_products` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 商品基本信息
  `item_id` varchar(50) NOT NULL COMMENT '商品ID',
  `title` text COMMENT '商品标题',
  `description` longtext COMMENT '商品描述',
  `sold_price` decimal(10,2) DEFAULT '0.00' COMMENT '售价',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '原价',
  `quantity` int(11) DEFAULT '0' COMMENT '库存数量',
  `item_status` tinyint(4) DEFAULT '0' COMMENT '商品状态(0:在线)',
  `item_status_str` varchar(20) DEFAULT '' COMMENT '商品状态文字',
  
  -- 统计数据
  `browse_cnt` int(11) DEFAULT '0' COMMENT '浏览量',
  `want_cnt` int(11) DEFAULT '0' COMMENT '想要人数',
  `favor_cnt` int(11) DEFAULT '0' COMMENT '收藏数',
  `collect_cnt` int(11) DEFAULT '0' COMMENT '收藏数(备用字段)',
  
  -- 分类信息
  `category_id` bigint(20) DEFAULT '0' COMMENT '分类ID',
  `cat_id` bigint(20) DEFAULT '0' COMMENT '分类ID(备用)',
  `channel_cat_id` bigint(20) DEFAULT '0' COMMENT '渠道分类ID',
  `root_channel_cat_id` bigint(20) DEFAULT '0' COMMENT '根分类ID',
  `level2_channel_cat_id` bigint(20) DEFAULT '0' COMMENT '二级分类ID',
  `level3_channel_cat_id` bigint(20) DEFAULT '0' COMMENT '三级分类ID',
  
  -- 促销信息
  `promotion_name` varchar(100) DEFAULT '' COMMENT '促销名称',
  `promotion_desc` varchar(200) DEFAULT '' COMMENT '促销描述',
  `promotion_price_min` decimal(10,2) DEFAULT '0.00' COMMENT '促销最低价',
  `min_discount_fee` decimal(10,2) DEFAULT '0.00' COMMENT '最小折扣金额',
  
  -- 图片信息
  `main_image_url` text COMMENT '主图URL',
  `image_count` tinyint(4) DEFAULT '0' COMMENT '图片数量',
  `all_image_urls` json COMMENT '所有图片URL(JSON格式)',
  
  -- 标签信息
  `item_label_text` varchar(100) DEFAULT '' COMMENT '商品标签文字',
  `item_label_channel_cate_id` bigint(20) DEFAULT '0' COMMENT '标签分类ID',
  
  -- 分类名称
  `good_name` varchar(100) DEFAULT '' COMMENT '商品分类名称',
  `bucket` varchar(10) DEFAULT '' COMMENT '分桶信息',
  `user_num` varchar(20) DEFAULT '' COMMENT '用户数量',
  
  -- 卖家关联
  `seller_id` varchar(50) NOT NULL COMMENT '卖家ID',
  
  -- 追踪参数
  `seller_bucket_id` varchar(10) DEFAULT '' COMMENT '卖家分桶ID',
  `idle_label_bucket_id` varchar(10) DEFAULT '' COMMENT '标签分桶ID',
  
  -- 时间信息
  `gmt_create` bigint(20) DEFAULT '0' COMMENT '商品创建时间戳',
  `gmt_create_date` varchar(30) DEFAULT '' COMMENT '商品创建时间字符串',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_id` (`item_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_channel_cat_id` (`channel_cat_id`),
  KEY `idx_gmt_create` (`gmt_create`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='闲鱼商品信息表';

-- =============================================
-- 2. 卖家信息表
-- =============================================
DROP TABLE IF EXISTS `xianyu_sellers`;
CREATE TABLE `xianyu_sellers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 卖家基本信息
  `seller_id` varchar(50) NOT NULL COMMENT '卖家ID',
  `nick` varchar(100) DEFAULT '' COMMENT '卖家昵称',
  `unique_name` varchar(100) DEFAULT '' COMMENT '唯一名称',
  `city` varchar(50) DEFAULT '' COMMENT '城市',
  `signature` text COMMENT '个性签名',
  `portrait_url` text COMMENT '头像URL',
  
  -- 统计信息
  `item_count` int(11) DEFAULT '0' COMMENT '商品数量',
  `has_sold_num` int(11) DEFAULT '0' COMMENT '已售数量',
  `user_reg_day` int(11) DEFAULT '0' COMMENT '注册天数',
  `register_time` bigint(20) DEFAULT '0' COMMENT '注册时间戳',
  `last_visit_time` varchar(50) DEFAULT '' COMMENT '最后访问时间',
  
  -- 信用信息
  `zhima_auth` tinyint(1) DEFAULT '0' COMMENT '芝麻认证(0:否,1:是)',
  `zhima_level_code` varchar(10) DEFAULT '' COMMENT '芝麻信用等级代码',
  `zhima_level_name` varchar(50) DEFAULT '' COMMENT '芝麻信用等级名称',
  `reply_ratio_24h` varchar(10) DEFAULT '' COMMENT '24小时回复率',
  `reply_interval` varchar(20) DEFAULT '' COMMENT '平均回复时间',
  `reply_ratio_double` decimal(5,4) DEFAULT '0.0000' COMMENT '24小时回复率(数值)',
  `avg_reply_30d` int(11) DEFAULT '0' COMMENT '30天平均回复时间(分钟)',
  
  -- 评价统计
  `good_remark_cnt` int(11) DEFAULT '0' COMMENT '好评数',
  `bad_remark_cnt` int(11) DEFAULT '0' COMMENT '差评数',
  `default_remark_cnt` int(11) DEFAULT '0' COMMENT '默认评价数',
  
  -- 信用标签
  `seller_level` varchar(10) DEFAULT '' COMMENT '卖家等级',
  
  -- 时间信息
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_seller_id` (`seller_id`),
  KEY `idx_city` (`city`),
  KEY `idx_seller_level` (`seller_level`),
  KEY `idx_zhima_level` (`zhima_level_code`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='闲鱼卖家信息表';

-- =============================================
-- 3. 商品图片表 (可选，用于存储多张图片详情)
-- =============================================
DROP TABLE IF EXISTS `xianyu_product_images`;
CREATE TABLE `xianyu_product_images` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `item_id` varchar(50) NOT NULL COMMENT '商品ID',
  `image_url` text NOT NULL COMMENT '图片URL',
  `width_size` int(11) DEFAULT '0' COMMENT '图片宽度',
  `height_size` int(11) DEFAULT '0' COMMENT '图片高度',
  `is_major` tinyint(1) DEFAULT '0' COMMENT '是否主图(0:否,1:是)',
  `image_type` tinyint(4) DEFAULT '0' COMMENT '图片类型',
  `sort_order` tinyint(4) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_is_major` (`is_major`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='闲鱼商品图片表';

-- =============================================
-- 4. 数据采集日志表
-- =============================================
DROP TABLE IF EXISTS `xianyu_crawl_logs`;
CREATE TABLE `xianyu_crawl_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(50) NOT NULL COMMENT '任务ID',
  `seller_id` varchar(50) NOT NULL COMMENT '卖家ID',
  `total_products` int(11) DEFAULT '0' COMMENT '采集商品总数',
  `success_products` int(11) DEFAULT '0' COMMENT '成功商品数',
  `failed_products` int(11) DEFAULT '0' COMMENT '失败商品数',
  `total_details` int(11) DEFAULT '0' COMMENT '采集详情总数',
  `success_details` int(11) DEFAULT '0' COMMENT '成功详情数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `duration_seconds` int(11) DEFAULT '0' COMMENT '耗时(秒)',
  `status` varchar(20) DEFAULT 'running' COMMENT '状态(running,success,failed)',
  `error_message` text COMMENT '错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='闲鱼数据采集日志表';

SET FOREIGN_KEY_CHECKS = 1;
