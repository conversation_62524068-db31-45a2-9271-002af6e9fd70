-- =============================================
-- 闲鱼数据库索引优化脚本
-- 创建时间: 2025-07-30
-- 描述: 针对常用查询场景的索引优化
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;

-- =============================================
-- 1. 商品表索引优化
-- =============================================

-- 复合索引：按卖家查询商品（最常用）
ALTER TABLE `xianyu_products` ADD INDEX `idx_seller_status_create` (`seller_id`, `item_status`, `gmt_create`);

-- 复合索引：按分类查询商品
ALTER TABLE `xianyu_products` ADD INDEX `idx_category_price_browse` (`channel_cat_id`, `sold_price`, `browse_cnt`);

-- 复合索引：按价格范围查询
ALTER TABLE `xianyu_products` ADD INDEX `idx_price_range_status` (`sold_price`, `item_status`, `created_at`);

-- 复合索引：按热度排序
ALTER TABLE `xianyu_products` ADD INDEX `idx_popularity` (`browse_cnt`, `want_cnt`, `favor_cnt`);

-- 全文索引：商品标题和描述搜索
ALTER TABLE `xianyu_products` ADD FULLTEXT INDEX `ft_title_desc` (`title`, `description`);

-- =============================================
-- 2. 卖家表索引优化
-- =============================================

-- 复合索引：按地区和等级查询卖家
ALTER TABLE `xianyu_sellers` ADD INDEX `idx_city_level` (`city`, `seller_level`, `zhima_level_code`);

-- 复合索引：按信用排序
ALTER TABLE `xianyu_sellers` ADD INDEX `idx_credit_ranking` (`zhima_level_code`, `good_remark_cnt`, `reply_ratio_double`);

-- 复合索引：按活跃度排序
ALTER TABLE `xianyu_sellers` ADD INDEX `idx_activity` (`has_sold_num`, `item_count`, `user_reg_day`);

-- =============================================
-- 3. 图片表索引优化
-- =============================================

-- 复合索引：按商品查询图片
ALTER TABLE `xianyu_product_images` ADD INDEX `idx_item_major_sort` (`item_id`, `is_major`, `sort_order`);

-- =============================================
-- 4. 日志表索引优化
-- =============================================

-- 复合索引：按任务查询日志
ALTER TABLE `xianyu_crawl_logs` ADD INDEX `idx_task_status_time` (`task_id`, `status`, `created_at`);

-- 复合索引：按卖家查询采集历史
ALTER TABLE `xianyu_crawl_logs` ADD INDEX `idx_seller_time_status` (`seller_id`, `created_at`, `status`);

-- =============================================
-- 5. 分区表建议（可选，适用于大数据量）
-- =============================================

-- 如果数据量很大，可以考虑按时间分区
-- 以下是分区示例（需要在创建表时使用）

/*
-- 按月分区的商品表示例
CREATE TABLE `xianyu_products_partitioned` (
    -- ... 所有字段定义 ...
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- =============================================
-- 6. 性能监控查询
-- =============================================

-- 查看表大小和行数
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    ROUND(DATA_LENGTH/1024/1024, 2) AS 'Data Size (MB)',
    ROUND(INDEX_LENGTH/1024/1024, 2) AS 'Index Size (MB)',
    ROUND((DATA_LENGTH + INDEX_LENGTH)/1024/1024, 2) AS 'Total Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE 'xianyu_%'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- 查看索引使用情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    SUB_PART,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE 'xianyu_%'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 查看慢查询（需要开启慢查询日志）
-- SHOW VARIABLES LIKE 'slow_query_log';
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 1;

-- =============================================
-- 7. 常用查询优化示例
-- =============================================

-- 优化前：全表扫描
-- SELECT * FROM xianyu_products WHERE title LIKE '%Python%';

-- 优化后：使用全文索引
-- SELECT * FROM xianyu_products WHERE MATCH(title, description) AGAINST('Python' IN NATURAL LANGUAGE MODE);

-- 优化前：多表关联无索引
-- SELECT p.*, s.nick FROM xianyu_products p JOIN xianyu_sellers s ON p.seller_id = s.seller_id WHERE p.sold_price > 10;

-- 优化后：使用复合索引
-- SELECT p.*, s.nick FROM xianyu_products p 
-- JOIN xianyu_sellers s ON p.seller_id = s.seller_id 
-- WHERE p.sold_price > 10 
-- ORDER BY p.browse_cnt DESC;

-- =============================================
-- 8. 维护脚本
-- =============================================

-- 定期优化表
-- OPTIMIZE TABLE xianyu_products;
-- OPTIMIZE TABLE xianyu_sellers;
-- OPTIMIZE TABLE xianyu_product_images;
-- OPTIMIZE TABLE xianyu_crawl_logs;

-- 分析表统计信息
-- ANALYZE TABLE xianyu_products;
-- ANALYZE TABLE xianyu_sellers;

-- 检查表完整性
-- CHECK TABLE xianyu_products;
-- CHECK TABLE xianyu_sellers;
