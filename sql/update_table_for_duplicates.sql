-- 更新数据库表结构以支持重复插入
-- 移除唯一键约束，允许同一用户的多条历史记录

-- 1. 删除原有的唯一键约束
ALTER TABLE `xianyu_users` DROP INDEX `uk_user_id`;

-- 2. 添加普通索引以提高查询性能
ALTER TABLE `xianyu_users` ADD INDEX `idx_user_id` (`user_id`);

-- 3. 添加复合索引用于按用户和时间查询
ALTER TABLE `xianyu_users` ADD INDEX `idx_user_time` (`user_id`, `created_at`);

-- 4. 添加复合索引用于按用户和更新时间查询
ALTER TABLE `xianyu_users` ADD INDEX `idx_user_updated` (`user_id`, `updated_at`);

-- 验证索引创建
SHOW INDEX FROM `xianyu_users`;
