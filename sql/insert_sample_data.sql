-- =============================================
-- 闲鱼数据库示例数据插入脚本（单表设计）
-- 创建时间: 2025-07-30
-- 描述: 基于实际API响应数据的示例插入语句
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;

-- =============================================
-- 插入完整商品信息示例（包含商品和卖家信息）
-- =============================================
INSERT INTO `xianyu_products` (
    -- 商品基本信息
    `item_id`, `title`, `description`, `sold_price`, `original_price`, `quantity`,
    `item_status`, `item_status_str`,
    -- 商品统计数据
    `browse_cnt`, `want_cnt`, `favor_cnt`, `collect_cnt`,
    -- 商品分类信息
    `category_id`, `cat_id`, `channel_cat_id`, `root_channel_cat_id`,
    `level2_channel_cat_id`, `level3_channel_cat_id`,
    -- 商品促销信息
    `promotion_name`, `promotion_desc`, `promotion_price_min`, `min_discount_fee`,
    -- 商品图片信息
    `main_image_url`, `image_count`, `all_image_urls`,
    -- 商品标签信息
    `item_label_text`, `item_label_channel_cate_id`,
    -- 商品分类名称
    `good_name`, `bucket`, `user_num`,
    -- 卖家基本信息
    `seller_id`, `seller_nick`, `seller_unique_name`, `seller_city`, `seller_signature`, `seller_portrait_url`,
    -- 卖家统计信息
    `seller_item_count`, `seller_has_sold_num`, `seller_user_reg_day`, `seller_register_time`, `seller_last_visit_time`,
    -- 卖家信用信息
    `seller_zhima_auth`, `seller_zhima_level_code`, `seller_zhima_level_name`,
    `seller_reply_ratio_24h`, `seller_reply_interval`, `seller_reply_ratio_double`, `seller_avg_reply_30d`,
    -- 卖家评价统计
    `seller_good_remark_cnt`, `seller_bad_remark_cnt`, `seller_default_remark_cnt`,
    -- 卖家信用标签
    `seller_level`,
    -- 追踪参数
    `seller_bucket_id`, `idle_label_bucket_id`,
    -- 时间信息
    `gmt_create`, `gmt_create_date`
) VALUES (
    -- 商品基本信息
    '958588805033',
    '[hot]【学生优惠】Python爬虫只有学生价 采集数据',
    '[hot]【学生优惠】Python爬虫只有学生价 采集数据 网站数据抓取定制\n\n[火]抓取微博、豆瓣、小红书、抖音、美团网页公开数据评论、文章、图片\n \n[牛]爬虫价格根据难度而定！\n[呲牙]可提供代码，全天在线，物美价廉\n[红圆]就算不买，问一问也没关系哦！',
    1.00, 0.00, 999, 0, '在线',
    -- 商品统计数据
    5, 0, 0, 0,
    -- 商品分类信息
    50023914, 50023914, 202036301, 201155301, 202028703, 202027703,
    -- 商品促销信息
    '2人小刀价', '直接买 ￥1.00', 0.80, 0.20,
    -- 商品图片信息
    'http://img.alicdn.com/bao/uploaded/i4/O1CN018SQsLF27gMgkiPbcl_!!4611686018427380514-0-fleamarket.jpg',
    5,
    JSON_ARRAY(
        'http://img.alicdn.com/bao/uploaded/i4/O1CN018SQsLF27gMgkiPbcl_!!4611686018427380514-0-fleamarket.jpg',
        'http://img.alicdn.com/bao/uploaded/i3/O1CN01hbrydd27gMglRvMUg_!!4611686018427380514-0-fleamarket.jpg',
        'http://img.alicdn.com/bao/uploaded/i2/O1CN011sWCSD27gMgjg2O6B_!!4611686018427380514-0-fleamarket.jpg',
        'http://img.alicdn.com/bao/uploaded/i3/O1CN01NIz98927gMgestpoT_!!4611686018427380514-0-fleamarket.jpg',
        'http://img.alicdn.com/bao/uploaded/i2/O1CN01Gi8faP27gMgkPK6Zr_!!4611686018427380514-0-fleamarket.jpg'
    ),
    -- 商品标签信息
    '电子资料', 202036301,
    -- 商品分类名称
    '电子资料', '3', '1111w',
    -- 卖家基本信息
    '2216036737826', '数据提取专家', '数据提取专家', '杭州',
    '数据可见即可提取，已读不回直接拉黑。',
    'http://img.alicdn.com/bao/uploaded/i3/O1CN01knQDNb27gMdjWtkEs_!!4611686018427380514-0-mtopupload.jpg',
    -- 卖家统计信息
    75, 1133, 765, 1687708938000, '刚刚来过',
    -- 卖家信用信息
    1, 'B', '信用优秀', '98%', '3分钟', 0.9839, 3,
    -- 卖家评价统计
    95, 1, 0,
    -- 卖家信用标签
    '5',
    -- 追踪参数
    '14', '13',
    -- 时间信息
    1753685676000, '2025-07-28 14:54:36'
) ON DUPLICATE KEY UPDATE
    -- 更新商品信息
    `title` = VALUES(`title`),
    `description` = VALUES(`description`),
    `sold_price` = VALUES(`sold_price`),
    `original_price` = VALUES(`original_price`),
    `quantity` = VALUES(`quantity`),
    `item_status` = VALUES(`item_status`),
    `item_status_str` = VALUES(`item_status_str`),
    `browse_cnt` = VALUES(`browse_cnt`),
    `want_cnt` = VALUES(`want_cnt`),
    `favor_cnt` = VALUES(`favor_cnt`),
    `collect_cnt` = VALUES(`collect_cnt`),
    `category_id` = VALUES(`category_id`),
    `cat_id` = VALUES(`cat_id`),
    `channel_cat_id` = VALUES(`channel_cat_id`),
    `root_channel_cat_id` = VALUES(`root_channel_cat_id`),
    `level2_channel_cat_id` = VALUES(`level2_channel_cat_id`),
    `level3_channel_cat_id` = VALUES(`level3_channel_cat_id`),
    `promotion_name` = VALUES(`promotion_name`),
    `promotion_desc` = VALUES(`promotion_desc`),
    `promotion_price_min` = VALUES(`promotion_price_min`),
    `min_discount_fee` = VALUES(`min_discount_fee`),
    `main_image_url` = VALUES(`main_image_url`),
    `image_count` = VALUES(`image_count`),
    `all_image_urls` = VALUES(`all_image_urls`),
    `item_label_text` = VALUES(`item_label_text`),
    `item_label_channel_cate_id` = VALUES(`item_label_channel_cate_id`),
    `good_name` = VALUES(`good_name`),
    `bucket` = VALUES(`bucket`),
    `user_num` = VALUES(`user_num`),
    -- 更新卖家信息
    `seller_nick` = VALUES(`seller_nick`),
    `seller_unique_name` = VALUES(`seller_unique_name`),
    `seller_city` = VALUES(`seller_city`),
    `seller_signature` = VALUES(`seller_signature`),
    `seller_portrait_url` = VALUES(`seller_portrait_url`),
    `seller_item_count` = VALUES(`seller_item_count`),
    `seller_has_sold_num` = VALUES(`seller_has_sold_num`),
    `seller_user_reg_day` = VALUES(`seller_user_reg_day`),
    `seller_register_time` = VALUES(`seller_register_time`),
    `seller_last_visit_time` = VALUES(`seller_last_visit_time`),
    `seller_zhima_auth` = VALUES(`seller_zhima_auth`),
    `seller_zhima_level_code` = VALUES(`seller_zhima_level_code`),
    `seller_zhima_level_name` = VALUES(`seller_zhima_level_name`),
    `seller_reply_ratio_24h` = VALUES(`seller_reply_ratio_24h`),
    `seller_reply_interval` = VALUES(`seller_reply_interval`),
    `seller_reply_ratio_double` = VALUES(`seller_reply_ratio_double`),
    `seller_avg_reply_30d` = VALUES(`seller_avg_reply_30d`),
    `seller_good_remark_cnt` = VALUES(`seller_good_remark_cnt`),
    `seller_bad_remark_cnt` = VALUES(`seller_bad_remark_cnt`),
    `seller_default_remark_cnt` = VALUES(`seller_default_remark_cnt`),
    `seller_level` = VALUES(`seller_level`),
    `seller_bucket_id` = VALUES(`seller_bucket_id`),
    `idle_label_bucket_id` = VALUES(`idle_label_bucket_id`),
    `gmt_create` = VALUES(`gmt_create`),
    `gmt_create_date` = VALUES(`gmt_create_date`),
    `updated_at` = CURRENT_TIMESTAMP;

-- =============================================
-- 插入采集日志示例
-- =============================================
INSERT INTO `xianyu_crawl_logs` (
    `task_id`, `seller_id`, `total_products`, `success_products`, `failed_products`,
    `total_details`, `success_details`, `start_time`, `end_time`, 
    `duration_seconds`, `status`, `error_message`
) VALUES (
    'task_20250730_001',
    '2216036737826',
    75,
    75,
    0,
    75,
    75,
    '2025-07-30 20:00:00',
    '2025-07-30 20:15:30',
    930,
    'success',
    NULL
);

-- =============================================
-- 查询示例（单表设计）
-- =============================================

-- 查询卖家及其商品统计
SELECT
    seller_id,
    seller_nick,
    seller_city,
    seller_level,
    seller_zhima_level_name,
    seller_item_count,
    seller_has_sold_num,
    COUNT(*) as actual_product_count,
    AVG(sold_price) as avg_price,
    SUM(browse_cnt) as total_browse_cnt
FROM xianyu_products
GROUP BY seller_id, seller_nick, seller_city, seller_level, seller_zhima_level_name, seller_item_count, seller_has_sold_num;

-- 查询热门商品
SELECT
    item_id,
    title,
    sold_price,
    browse_cnt,
    want_cnt,
    good_name,
    seller_nick,
    seller_city
FROM xianyu_products
ORDER BY browse_cnt DESC, want_cnt DESC
LIMIT 10;

-- 查询指定卖家的所有商品
SELECT
    item_id,
    title,
    sold_price,
    browse_cnt,
    want_cnt,
    item_status_str,
    gmt_create_date
FROM xianyu_products
WHERE seller_id = '2216036737826'
ORDER BY gmt_create DESC;

-- 按分类查询商品
SELECT
    good_name,
    COUNT(*) as product_count,
    AVG(sold_price) as avg_price,
    SUM(browse_cnt) as total_browse_cnt
FROM xianyu_products
GROUP BY good_name
ORDER BY product_count DESC;
