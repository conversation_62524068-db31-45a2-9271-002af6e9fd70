-- =============================================
-- 闲鱼数据库示例数据插入脚本
-- 创建时间: 2025-07-30
-- 描述: 基于实际API响应数据的示例插入语句
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;

-- =============================================
-- 1. 插入卖家信息示例
-- =============================================
INSERT INTO `xianyu_sellers` (
    `seller_id`, `nick`, `unique_name`, `city`, `signature`, `portrait_url`,
    `item_count`, `has_sold_num`, `user_reg_day`, `register_time`, `last_visit_time`,
    `zhima_auth`, `zhima_level_code`, `zhima_level_name`, 
    `reply_ratio_24h`, `reply_interval`, `reply_ratio_double`, `avg_reply_30d`,
    `good_remark_cnt`, `bad_remark_cnt`, `default_remark_cnt`, `seller_level`
) VALUES (
    '2216036737826', 
    '数据提取专家', 
    '数据提取专家', 
    '杭州', 
    '数据可见即可提取，已读不回直接拉黑。',
    'http://img.alicdn.com/bao/uploaded/i3/O1CN01knQDNb27gMdjWtkEs_!!4611686018427380514-0-mtopupload.jpg',
    75, 
    1133, 
    765, 
    1687708938000, 
    '刚刚来过',
    1, 
    'B', 
    '信用优秀',
    '98%', 
    '3分钟', 
    0.9839, 
    3,
    95, 
    1, 
    0, 
    '5'
) ON DUPLICATE KEY UPDATE
    `nick` = VALUES(`nick`),
    `unique_name` = VALUES(`unique_name`),
    `city` = VALUES(`city`),
    `signature` = VALUES(`signature`),
    `portrait_url` = VALUES(`portrait_url`),
    `item_count` = VALUES(`item_count`),
    `has_sold_num` = VALUES(`has_sold_num`),
    `user_reg_day` = VALUES(`user_reg_day`),
    `last_visit_time` = VALUES(`last_visit_time`),
    `zhima_auth` = VALUES(`zhima_auth`),
    `zhima_level_code` = VALUES(`zhima_level_code`),
    `zhima_level_name` = VALUES(`zhima_level_name`),
    `reply_ratio_24h` = VALUES(`reply_ratio_24h`),
    `reply_interval` = VALUES(`reply_interval`),
    `reply_ratio_double` = VALUES(`reply_ratio_double`),
    `avg_reply_30d` = VALUES(`avg_reply_30d`),
    `good_remark_cnt` = VALUES(`good_remark_cnt`),
    `bad_remark_cnt` = VALUES(`bad_remark_cnt`),
    `default_remark_cnt` = VALUES(`default_remark_cnt`),
    `seller_level` = VALUES(`seller_level`),
    `updated_at` = CURRENT_TIMESTAMP;

-- =============================================
-- 2. 插入商品信息示例
-- =============================================
INSERT INTO `xianyu_products` (
    `item_id`, `title`, `description`, `sold_price`, `original_price`, `quantity`,
    `item_status`, `item_status_str`, `browse_cnt`, `want_cnt`, `favor_cnt`, `collect_cnt`,
    `category_id`, `cat_id`, `channel_cat_id`, `root_channel_cat_id`, 
    `level2_channel_cat_id`, `level3_channel_cat_id`,
    `promotion_name`, `promotion_desc`, `promotion_price_min`, `min_discount_fee`,
    `main_image_url`, `image_count`, `all_image_urls`,
    `item_label_text`, `item_label_channel_cate_id`,
    `good_name`, `bucket`, `user_num`, `seller_id`,
    `seller_bucket_id`, `idle_label_bucket_id`,
    `gmt_create`, `gmt_create_date`
) VALUES (
    '958588805033',
    '[hot]【学生优惠】Python爬虫只有学生价 采集数据',
    '[hot]【学生优惠】Python爬虫只有学生价 采集数据 网站数据抓取定制\n\n[火]抓取微博、豆瓣、小红书、抖音、美团网页公开数据评论、文章、图片\n \n[牛]爬虫价格根据难度而定！\n[呲牙]可提供代码，全天在线，物美价廉\n[红圆]就算不买，问一问也没关系哦！',
    1.00,
    0.00,
    999,
    0,
    '在线',
    5,
    0,
    0,
    0,
    50023914,
    50023914,
    202036301,
    201155301,
    202028703,
    202027703,
    '2人小刀价',
    '直接买 ￥1.00',
    0.80,
    0.20,
    'http://img.alicdn.com/bao/uploaded/i4/O1CN018SQsLF27gMgkiPbcl_!!4611686018427380514-0-fleamarket.jpg',
    5,
    JSON_ARRAY(
        'http://img.alicdn.com/bao/uploaded/i4/O1CN018SQsLF27gMgkiPbcl_!!4611686018427380514-0-fleamarket.jpg',
        'http://img.alicdn.com/bao/uploaded/i3/O1CN01hbrydd27gMglRvMUg_!!4611686018427380514-0-fleamarket.jpg',
        'http://img.alicdn.com/bao/uploaded/i2/O1CN011sWCSD27gMgjg2O6B_!!4611686018427380514-0-fleamarket.jpg',
        'http://img.alicdn.com/bao/uploaded/i3/O1CN01NIz98927gMgestpoT_!!4611686018427380514-0-fleamarket.jpg',
        'http://img.alicdn.com/bao/uploaded/i2/O1CN01Gi8faP27gMgkPK6Zr_!!4611686018427380514-0-fleamarket.jpg'
    ),
    '电子资料',
    202036301,
    '电子资料',
    '3',
    '1111w',
    '2216036737826',
    '14',
    '13',
    1753685676000,
    '2025-07-28 14:54:36'
) ON DUPLICATE KEY UPDATE
    `title` = VALUES(`title`),
    `description` = VALUES(`description`),
    `sold_price` = VALUES(`sold_price`),
    `original_price` = VALUES(`original_price`),
    `quantity` = VALUES(`quantity`),
    `item_status` = VALUES(`item_status`),
    `item_status_str` = VALUES(`item_status_str`),
    `browse_cnt` = VALUES(`browse_cnt`),
    `want_cnt` = VALUES(`want_cnt`),
    `favor_cnt` = VALUES(`favor_cnt`),
    `collect_cnt` = VALUES(`collect_cnt`),
    `category_id` = VALUES(`category_id`),
    `cat_id` = VALUES(`cat_id`),
    `channel_cat_id` = VALUES(`channel_cat_id`),
    `root_channel_cat_id` = VALUES(`root_channel_cat_id`),
    `level2_channel_cat_id` = VALUES(`level2_channel_cat_id`),
    `level3_channel_cat_id` = VALUES(`level3_channel_cat_id`),
    `promotion_name` = VALUES(`promotion_name`),
    `promotion_desc` = VALUES(`promotion_desc`),
    `promotion_price_min` = VALUES(`promotion_price_min`),
    `min_discount_fee` = VALUES(`min_discount_fee`),
    `main_image_url` = VALUES(`main_image_url`),
    `image_count` = VALUES(`image_count`),
    `all_image_urls` = VALUES(`all_image_urls`),
    `item_label_text` = VALUES(`item_label_text`),
    `item_label_channel_cate_id` = VALUES(`item_label_channel_cate_id`),
    `good_name` = VALUES(`good_name`),
    `bucket` = VALUES(`bucket`),
    `user_num` = VALUES(`user_num`),
    `seller_bucket_id` = VALUES(`seller_bucket_id`),
    `idle_label_bucket_id` = VALUES(`idle_label_bucket_id`),
    `gmt_create` = VALUES(`gmt_create`),
    `gmt_create_date` = VALUES(`gmt_create_date`),
    `updated_at` = CURRENT_TIMESTAMP;

-- =============================================
-- 3. 插入商品图片详情示例
-- =============================================
INSERT INTO `xianyu_product_images` (
    `item_id`, `image_url`, `width_size`, `height_size`, `is_major`, `image_type`, `sort_order`
) VALUES 
('958588805033', 'http://img.alicdn.com/bao/uploaded/i4/O1CN018SQsLF27gMgkiPbcl_!!4611686018427380514-0-fleamarket.jpg', 326, 315, 1, 0, 1),
('958588805033', 'http://img.alicdn.com/bao/uploaded/i3/O1CN01hbrydd27gMglRvMUg_!!4611686018427380514-0-fleamarket.jpg', 891, 729, 0, 0, 2),
('958588805033', 'http://img.alicdn.com/bao/uploaded/i2/O1CN011sWCSD27gMgjg2O6B_!!4611686018427380514-0-fleamarket.jpg', 1547, 1443, 0, 0, 3),
('958588805033', 'http://img.alicdn.com/bao/uploaded/i3/O1CN01NIz98927gMgestpoT_!!4611686018427380514-0-fleamarket.jpg', 1290, 1289, 0, 0, 4),
('958588805033', 'http://img.alicdn.com/bao/uploaded/i2/O1CN01Gi8faP27gMgkPK6Zr_!!4611686018427380514-0-fleamarket.jpg', 1290, 996, 0, 0, 5)
ON DUPLICATE KEY UPDATE
    `width_size` = VALUES(`width_size`),
    `height_size` = VALUES(`height_size`),
    `is_major` = VALUES(`is_major`),
    `image_type` = VALUES(`image_type`),
    `sort_order` = VALUES(`sort_order`);

-- =============================================
-- 4. 插入采集日志示例
-- =============================================
INSERT INTO `xianyu_crawl_logs` (
    `task_id`, `seller_id`, `total_products`, `success_products`, `failed_products`,
    `total_details`, `success_details`, `start_time`, `end_time`, 
    `duration_seconds`, `status`, `error_message`
) VALUES (
    'task_20250730_001',
    '2216036737826',
    75,
    75,
    0,
    75,
    75,
    '2025-07-30 20:00:00',
    '2025-07-30 20:15:30',
    930,
    'success',
    NULL
);

-- =============================================
-- 5. 查询示例
-- =============================================

-- 查询卖家及其商品统计
SELECT 
    s.seller_id,
    s.nick,
    s.city,
    s.seller_level,
    s.zhima_level_name,
    s.item_count,
    s.has_sold_num,
    COUNT(p.id) as actual_product_count,
    AVG(p.sold_price) as avg_price,
    SUM(p.browse_cnt) as total_browse_cnt
FROM xianyu_sellers s
LEFT JOIN xianyu_products p ON s.seller_id = p.seller_id
GROUP BY s.seller_id;

-- 查询热门商品
SELECT 
    p.item_id,
    p.title,
    p.sold_price,
    p.browse_cnt,
    p.want_cnt,
    p.good_name,
    s.nick as seller_nick,
    s.city as seller_city
FROM xianyu_products p
LEFT JOIN xianyu_sellers s ON p.seller_id = s.seller_id
ORDER BY p.browse_cnt DESC, p.want_cnt DESC
LIMIT 10;
