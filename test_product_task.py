#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试商品采集任务
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 直接导入文件，避免 __init__.py 的依赖问题
import importlib.util
spec = importlib.util.spec_from_file_location("xianyu_product_task", "tasks/xianyu_product_task.py")
xianyu_product_task = importlib.util.module_from_spec(spec)
spec.loader.exec_module(xianyu_product_task)
XianYuProDuctTask = xianyu_product_task.XianYuProDuctTask
from loguru import logger

async def test_product_task():
    """测试商品采集任务"""
    logger.info("🧪 开始测试商品采集任务")
    
    # 创建任务实例
    task = XianYuProDuctTask("test_product_task")
    
    try:
        # 执行任务
        result = await task.execute()
        
        # 打印结果
        logger.info("📋 任务执行结果:")
        logger.info(f"   状态: {result.get('status')}")
        logger.info(f"   消息: {result.get('message')}")
        logger.info(f"   总用户数: {result.get('total_users', 0)}")
        logger.info(f"   成功用户数: {result.get('successful_users', 0)}")
        logger.info(f"   失败用户数: {result.get('failed_users', 0)}")
        logger.info(f"   总商品数: {result.get('total_products', 0)}")
        
        # 显示示例商品
        sample_products = result.get('sample_products', [])
        if sample_products:
            logger.info("🛍️ 示例商品:")
            for i, product in enumerate(sample_products, 1):
                logger.info(f"   {i}. {product.get('title', '未知标题')} - ¥{product.get('price', '0')}")
                logger.info(f"      商品ID: {product.get('item_id', '未知')}")
                logger.info(f"      卖家ID: {product.get('seller_id', '未知')}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 任务执行失败: {e}")
        return None

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(test_product_task())
    
    if result and result.get('status') == 'success':
        logger.info("✅ 测试完成！")
    else:
        logger.error("❌ 测试失败！")
