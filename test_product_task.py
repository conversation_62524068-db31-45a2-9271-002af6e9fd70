#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试商品采集任务
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 直接导入文件，避免 __init__.py 的依赖问题
import importlib.util
spec = importlib.util.spec_from_file_location("xianyu_product_task", "tasks/xianyu_product_task.py")
xianyu_product_task = importlib.util.module_from_spec(spec)
spec.loader.exec_module(xianyu_product_task)
XianYuProDuctTask = xianyu_product_task.XianYuProDuctTask
from loguru import logger

async def test_product_task():
    """测试商品采集任务（包含所有商品详情）"""
    logger.info("🧪 开始测试商品采集任务（包含所有商品详情）")

    # 创建任务实例
    task = XianYuProDuctTask("test_product_task")

    try:
        # 执行任务：获取所有商品列表和详情
        result = await task.execute()  # 获取所有商品的详情

        # 打印结果
        logger.info("📋 任务执行结果:")
        logger.info(f"   状态: {result.get('status')}")
        logger.info(f"   消息: {result.get('message')}")
        logger.info(f"   总用户数: {result.get('total_users', 0)}")
        logger.info(f"   成功用户数: {result.get('successful_users', 0)}")
        logger.info(f"   失败用户数: {result.get('failed_users', 0)}")
        logger.info(f"   总商品数: {result.get('total_products', 0)}")
        logger.info(f"   总详情数: {result.get('total_details', 0)}")

        # 显示示例商品
        sample_products = result.get('sample_products', [])
        if sample_products:
            logger.info("🛍️ 示例商品:")
            for i, product in enumerate(sample_products, 1):
                logger.info(f"   {i}. {product.get('title', '未知标题')} - ¥{product.get('price', '0')}")
                logger.info(f"      商品ID: {product.get('item_id', '未知')}")
                logger.info(f"      卖家ID: {product.get('seller_id', '未知')}")

                # 如果有详情信息，显示详情
                if product.get('detail_title'):
                    logger.info(f"      📝 详情标题: {product.get('detail_title', '未知')}")
                    logger.info(f"      👀 浏览量: {product.get('browse_cnt', 0)}")
                    logger.info(f"      ❤️ 想要人数: {product.get('want_cnt', 0)}")
                    logger.info(f"      🏪 卖家昵称: {product.get('seller_nick', '未知')}")
                    logger.info(f"      📍 卖家城市: {product.get('seller_city', '未知')}")
                    logger.info(f"      💰 原价: ¥{product.get('original_price', '0')}")
                    logger.info(f"      📦 库存: {product.get('quantity', 0)}")
                else:
                    logger.info(f"      ⚠️ 未获取到详情信息")

        return result

    except Exception as e:
        logger.error(f"❌ 任务执行失败: {e}")
        return None

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(test_product_task())
    
    if result and result.get('status') == 'success':
        logger.info("✅ 测试完成！")
    else:
        logger.error("❌ 测试失败！")
