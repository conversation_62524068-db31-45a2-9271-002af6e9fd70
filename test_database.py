#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接和商品数据保存
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.xianyu_product import XianyuProduct
from config.database import get_database_config
from loguru import logger

async def test_database_connection():
    """测试数据库连接"""
    logger.info("🧪 开始测试数据库连接")
    
    try:
        # 获取数据库配置
        config = get_database_config()
        db_config = {
            'host': config['host'],
            'port': config['port'],
            'user': config['user'],
            'password': config['password'],
            'database': config['db']
        }
        
        logger.info(f"📋 数据库配置: {config['host']}:{config['port']}/{config['db']}")
        
        # 创建数据模型
        product_model = XianyuProduct(db_config)
        
        # 初始化连接池
        await product_model.init_db_pool()
        logger.info("✅ 数据库连接成功")
        
        # 关闭连接池
        await product_model.close_db_pool()
        logger.info("✅ 数据库连接关闭成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

async def test_product_save():
    """测试商品数据保存"""
    logger.info("🧪 开始测试商品数据保存")
    
    try:
        # 获取数据库配置
        config = get_database_config()
        db_config = {
            'host': config['host'],
            'port': config['port'],
            'user': config['user'],
            'password': config['password'],
            'database': config['db']
        }
        
        # 创建数据模型
        product_model = XianyuProduct(db_config)
        
        # 测试商品数据
        test_product = {
            # 商品基本信息
            'item_id': 'test_' + str(int(asyncio.get_event_loop().time())),
            'title': '测试商品标题',
            'description': '这是一个测试商品的描述信息',
            'sold_price': '9.99',
            'original_price': '19.99',
            'quantity': 100,
            'item_status': 0,
            'item_status_str': '在线',
            
            # 商品统计数据
            'browse_cnt': 10,
            'want_cnt': 5,
            'favor_cnt': 3,
            'collect_cnt': 2,
            
            # 商品分类信息
            'category_id': 12345,
            'cat_id': 12345,
            'channel_cat_id': 67890,
            'root_channel_cat_id': 11111,
            'level2_channel_cat_id': 22222,
            'level3_channel_cat_id': 33333,
            
            # 商品促销信息
            'promotion_name': '测试促销',
            'promotion_desc': '限时优惠',
            'promotion_price_min': '8.99',
            'min_discount_fee_str': '1.00',
            
            # 商品图片信息
            'main_image_url': 'https://example.com/image1.jpg',
            'image_count': 3,
            'all_image_urls': [
                'https://example.com/image1.jpg',
                'https://example.com/image2.jpg',
                'https://example.com/image3.jpg'
            ],
            
            # 商品标签信息
            'item_label_text': '测试标签',
            'item_label_channel_cate_id': 44444,
            
            # 商品分类名称
            'good_name': '测试分类',
            'bucket': '1',
            'user_num': '100w',
            
            # 卖家基本信息
            'seller_id': 'test_seller_123',
            'seller_nick': '测试卖家',
            'seller_unique_name': '测试卖家唯一名',
            'seller_city': '测试城市',
            'seller_signature': '测试卖家签名',
            'seller_portrait_url': 'https://example.com/avatar.jpg',
            
            # 卖家统计信息
            'seller_item_count': 50,
            'seller_has_sold_num': 200,
            'seller_user_reg_day': 365,
            'seller_register_time': 1640995200000,
            'seller_last_visit_time': '刚刚来过',
            
            # 卖家信用信息
            'seller_zhima_auth': 1,
            'seller_zhima_level_code': 'A',
            'seller_zhima_level_name': '信用极好',
            'seller_reply_ratio_24h': '99%',
            'seller_reply_interval': '1分钟',
            'seller_reply_ratio_double': 0.99,
            'seller_avg_reply_30d': 1,
            
            # 卖家评价统计
            'seller_good_remark_cnt': 100,
            'seller_bad_remark_cnt': 0,
            'seller_default_remark_cnt': 0,
            
            # 卖家信用标签
            'seller_level': '5',
            
            # 追踪参数
            'seller_bucket_id': '10',
            'idle_label_bucket_id': '20',
            
            # 时间信息
            'gmt_create': 1640995200000,
            'gmt_create_date': '2022-01-01 00:00:00'
        }
        
        # 保存测试商品
        success = await product_model.save_product(test_product)
        
        if success:
            logger.info("✅ 测试商品保存成功")
        else:
            logger.error("❌ 测试商品保存失败")
        
        # 关闭连接池
        await product_model.close_db_pool()
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 测试商品保存异常: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始数据库测试")
    
    # 测试数据库连接
    connection_ok = await test_database_connection()
    
    if connection_ok:
        # 测试商品数据保存
        save_ok = await test_product_save()
        
        if save_ok:
            logger.info("🎉 所有数据库测试通过！")
        else:
            logger.error("❌ 商品保存测试失败！")
    else:
        logger.error("❌ 数据库连接测试失败！")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
