import random

import aiohttp
from datetime import datetime
from scheduler import BaseTask
import time
import json
from goofish_monitor import GoofishCookieMonitor
from loguru import logger

from utils.xianyu_utils import trans_cookies, generate_sign, generate_log_id
from models.xianyu_user import Xianyu<PERSON>ser
from db.mysql_manager import get_db_manager


class XianYuProDuctTask(BaseTask):
    """
    闲鱼用户采集任务

    """
    
    def __init__(self, task_id: str):
        super().__init__(
            task_id=task_id,
            name="闲鱼用户商品采集任务",
            description="闲鱼用户商品采集任务"
        )
        self.execution_count = 0
        self.users =[
            {
                "user_id":"2201292691262",
                "user_name":"Auroral",

            },
            {
                "user_id": "2216036737826",
                "user_name": "数据提取专家",

            },
            {
                "user_id": "3471902439",
                "user_name": "Esterketone",

            },
            {
                "user_id": "48549962",
                "user_name": "生活幻想家",

            },
            {
                "user_id": "2658142545",
                "user_name": "写代码赚猫粮",

            },
        ]
        self.url = f"https://h5api.m.goofish.com/h5/mtop.idle.web.xyh.item.list/1.0/"
         # 设置通用headers
        self.headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'referer': 'https://www.goofish.com/',
            "content-type": "application/x-www-form-urlencoded",
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        }
    async def execute(self):
        """
        异步执行闲鱼用户采集任务

        Returns:
            dict: 执行结果
        """
        self.execution_count += 1
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(self.users)



        # 核心功能：打印测试消息
        logger.info("闲鱼用户采集任务")
        monitor = GoofishCookieMonitor()

        # 直接使用原始方法获取目标Cookie（现在在同一事件循环中）
        cookies_str = await monitor.get_cookies_string()
        logger.info(cookies_str)
        cookies_dict = trans_cookies(cookies_str) if cookies_str else {}

        self.headers['cookie'] = cookies_str
        logger.info(self.headers)
        # {seller_id} ({seller_name or '未知'})
        logger.info(f"开始监控卖家: ")
        timestamp = str(int(time.time() * 1000))
        page_number = 1

        data_payload = {
            "needGroupInfo": False,
            "pageNumber": page_number,
            "userId": "2201292691262",
            "pageSize": 20,
            "groupName": "在售",
            "groupId": 46476081,
            "defaultGroup": True
        }
        # 构建请求数据
        data_val = json.dumps(data_payload)
        # 生成签名
        sign = generate_sign(timestamp, cookies_dict['_m_h5_tk'].split('_')[0], data_val)

        # 生成 log_id
        log_id = generate_log_id()

        # 构造 spm_pre 参数
        spm_pre_base = f"a21ybx.item.itemHeader.{random.randint(1, 10)}"  # 根据接口需要调整
        spm_pre = f"{spm_pre_base}.{log_id}"
        params = {
            "jsv": "2.7.2",
            "appKey": "********",
            "t": timestamp,
            "sign": sign,
            "v": "1.0",
            "type": "originaljson",
            "accountSite": "xianyu",
            "dataType": "json",
            "timeout": "20000",
            "api": "mtop.idle.web.xyh.item.list",
            "sessionOption": "AutoLoginOnly",
            "spm_cnt": "a21ybx.item.0.0",
            "spm_pre": spm_pre,
            "log_id": log_id
        }

        # 构建请求体
        data = {"data": data_val}
        logger.info(data)

        async with aiohttp.ClientSession() as session:
            # 发送异步请求
            async with session.post(
                    self.url,
                    headers=self.headers,
                    params=params,
                    data=data
            ) as response:
                api_result = await response.json()
                logger.info(f"API响应: {api_result}")










        # 返回执行结果
        result = {
            'status': 'success',
            'message': '闲鱼用户采集任务执行完成',
            'execution_time': current_time,
            'execution_count': self.execution_count,
            'cookies_string': cookies_str,

        }

        return result
    
    def on_success(self, result):
        """任务成功回调"""
        execution_count = result.get('execution_count', 0)
        logger.info(f"✅ 闲鱼用户商品采集任务 (第{execution_count}次)")
    
    def on_error(self, error):
        """任务失败回调"""
        logger.info(f"❌ 闲鱼用户商品采集任务: {error}")
