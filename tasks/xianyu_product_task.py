import asyncio
import random

import aiohttp
from datetime import datetime
from scheduler import BaseTask
import time
import json
from goofish_monitor import GoofishCookieMonitor
from loguru import logger

from utils.xianyu_utils import trans_cookies, generate_sign, generate_log_id


class XianYuProDuctTask(BaseTask):
    """
    闲鱼商品采集任务
    """
    
    def __init__(self, task_id: str):
        super().__init__(
            task_id=task_id,
            name="闲鱼商品采集任务",
            description="闲鱼商品采集任务"
        )
        self.execution_count = 0
        self.users = [
            {
                "user_id": "2201292691262",
                "user_name": "Auroral",
            },
            {
                "user_id": "2216036737826",
                "user_name": "数据提取专家",
            },
            {
                "user_id": "3471902439",
                "user_name": "Esterketone",
            },
            {
                "user_id": "48549962",
                "user_name": "生活幻想家",
            },
            {
                "user_id": "2658142545",
                "user_name": "写代码赚猫粮",
            },
        ]
        self.url = f"https://h5api.m.goofish.com/h5/mtop.idle.web.xyh.item.list/1.0/"
        # 设置通用headers
        self.headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'referer': 'https://www.goofish.com/',
            "content-type": "application/x-www-form-urlencoded",
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        }
    
    async def get_seller_products(self, seller_id: str, page_number: int, cookies_str: str, max_retry: int = 3):
        """
        获取卖家指定页面的商品列表
        
        Args:
            seller_id: 卖家用户ID
            page_number: 页码
            cookies_str: Cookie字符串
            max_retry: 最大重试次数
            
        Returns:
            dict: API响应数据，包含商品列表和分页信息
        """
        for attempt in range(max_retry):
            try:
                # 解析Cookie
                cookies_dict = trans_cookies(cookies_str) if cookies_str else {}
                if not cookies_dict.get('_m_h5_tk'):
                    logger.error("Cookie中缺少_m_h5_tk字段")
                    return None
                
                # 构建请求数据
                data_payload = {
                    "needGroupInfo": False,
                    "pageNumber": page_number,
                    "userId": seller_id,
                    "pageSize": 20,
                    "groupName": "在售",
                    "groupId": 46476081,
                    "defaultGroup": True
                }
                
                # 生成时间戳和签名
                timestamp = str(int(time.time() * 1000))
                data_val = json.dumps(data_payload)
                sign = generate_sign(timestamp, cookies_dict['_m_h5_tk'].split('_')[0], data_val)
                
                # 生成 log_id
                log_id = generate_log_id()
                
                # 构造 spm_pre 参数
                spm_pre_base = f"a21ybx.item.itemHeader.{random.randint(1, 10)}"
                spm_pre = f"{spm_pre_base}.{log_id}"
                
                # 构建请求参数
                params = {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "t": timestamp,
                    "sign": sign,
                    "v": "1.0",
                    "type": "originaljson",
                    "accountSite": "xianyu",
                    "dataType": "json",
                    "timeout": "20000",
                    "api": "mtop.idle.web.xyh.item.list",
                    "sessionOption": "AutoLoginOnly",
                    "spm_cnt": "a21ybx.item.0.0",
                    "spm_pre": spm_pre,
                    "log_id": log_id
                }
                
                # 构建请求体
                data = {"data": data_val}
                
                # 设置请求头
                headers = self.headers.copy()
                headers['cookie'] = cookies_str
                
                # 发起异步请求
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        self.url,
                        headers=headers,
                        params=params,
                        data=data,
                        timeout=30
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            
                            # 检查API响应状态
                            if result.get('ret') and result['ret'][0] == 'SUCCESS::调用成功':
                                logger.debug(f"成功获取卖家商品列表: {seller_id}, 页码: {page_number}")
                                return result
                            else:
                                logger.error(f"API调用失败: {result.get('ret', ['未知错误'])}")
                                return None
                        else:
                            logger.error(f"HTTP请求失败: {response.status}")
                            
            except Exception as e:
                logger.error(f"获取商品列表异常 (尝试 {attempt + 1}/{max_retry}): {seller_id}, 页码: {page_number} - {e}")
                if attempt < max_retry - 1:
                    await asyncio.sleep(1)  # 重试前等待1秒
                    continue
        
        logger.error(f"获取商品列表失败，已达到最大重试次数: {seller_id}, 页码: {page_number}")
        return None
    
    def _parse_product_from_card(self, card: dict, seller_id: str) -> dict:
        """
        从商品卡片数据中解析商品信息
        
        Args:
            card: 商品卡片数据
            seller_id: 卖家ID
            
        Returns:
            dict: 解析后的商品信息
        """
        try:
            card_data = card.get('cardData', {})
            
            # 基本商品信息
            product = {
                'item_id': card_data.get('id', ''),
                'seller_id': seller_id,
                'title': card_data.get('title', ''),
                'category_id': card_data.get('categoryId', ''),
                'auction_type': card_data.get('auctionType', ''),
                'item_status': card_data.get('itemStatus', 0),
                'detail_url': card_data.get('detailUrl', ''),
            }
            
            # 价格信息
            price_info = card_data.get('priceInfo', {})
            product.update({
                'price': price_info.get('price', ''),
                'price_pre_text': price_info.get('preText', ''),
            })
            
            # 图片信息
            pic_info = card_data.get('picInfo', {})
            product.update({
                'pic_url': pic_info.get('picUrl', ''),
                'pic_width': pic_info.get('width', 0),
                'pic_height': pic_info.get('height', 0),
                'has_video': pic_info.get('hasVideo', False),
            })
            
            # 详细参数
            detail_params = card_data.get('detailParams', {})
            product.update({
                'sold_price': detail_params.get('soldPrice', ''),
                'is_video': detail_params.get('isVideo', 'false'),
                'image_infos': detail_params.get('imageInfos', ''),
            })
            
            # 标签信息
            label_data_vo = card_data.get('itemLabelDataVO', {})
            label_data = label_data_vo.get('labelData', {})
            
            # 提取热销标签
            r2_tags = label_data.get('r2', {}).get('tagList', [])
            hot_sale_tag = ''
            if r2_tags:
                hot_sale_tag = r2_tags[0].get('data', {}).get('content', '')
            
            # 提取想要人数标签
            r3_tags = label_data.get('r3', {}).get('tagList', [])
            want_count_tag = ''
            if r3_tags:
                want_count_tag = r3_tags[0].get('data', {}).get('content', '')
            
            product.update({
                'hot_sale_tag': hot_sale_tag,
                'want_count_tag': want_count_tag,
                'label_bucket_id': label_data_vo.get('labelBucketId', ''),
            })
            
            # 追踪参数
            track_params = card_data.get('trackParams', {})
            product.update({
                'is_shop_user': track_params.get('isShopUser', ''),
                'label_bucket_id_track': track_params.get('labelBucketId', ''),
            })
            
            # 添加解析时间
            product['parsed_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            return product
            
        except Exception as e:
            logger.error(f"解析商品卡片数据失败: {e}")
            logger.debug(f"问题卡片数据: {card}")
            return None

    async def get_all_user_products(self, user_id: str, user_name: str, cookies_str: str,
                                   max_pages: int = 100, timeout_minutes: int = 10) -> list:
        """
        获取指定用户的所有商品（无商品数量限制）

        Args:
            user_id: 用户ID
            user_name: 用户名称
            cookies_str: Cookie字符串
            max_pages: 最大页数限制（防止无限循环）
            timeout_minutes: 超时限制（分钟）

        Returns:
            list: 用户的所有商品列表
        """
        all_products = []
        page_number = 1
        start_time = time.time()

        logger.info(f"开始获取用户所有商品: {user_id} ({user_name})")

        while True:
            # 超时检查
            if time.time() - start_time > timeout_minutes * 60:
                logger.warning(f"用户{user_id}商品采集超时({timeout_minutes}分钟)，停止采集")
                break

            # 页数限制检查
            if page_number > max_pages:
                logger.warning(f"用户{user_id}已达到最大页数限制: {max_pages}")
                break

            try:
                # 获取当前页商品
                response = await self.get_seller_products(user_id, page_number, cookies_str)
                if not response or not response.get('data'):
                    logger.warning(f"用户{user_id}获取第{page_number}页商品失败或无数据")
                    break

                data = response['data']
                card_list = data.get('cardList', [])

                if not card_list:
                    logger.info(f"用户{user_id}第{page_number}页无商品数据")
                    break

                # 解析当前页商品数据
                page_products = []
                for card in card_list:
                    product = self._parse_product_from_card(card, user_id)
                    if product:
                        page_products.append(product)

                all_products.extend(page_products)
                logger.info(f"用户{user_id}: 第{page_number}页获取到{len(page_products)}个商品，累计{len(all_products)}个")

                # 检查是否有下一页
                if not data.get('nextPage', False):
                    logger.info(f"用户{user_id}已获取所有商品页面")
                    break

                page_number += 1

                # 添加请求间隔，避免频繁调用
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"用户{user_id}获取第{page_number}页商品异常: {e}")
                break

        logger.info(f"用户{user_id} ({user_name}) 商品采集完成，共获取到{len(all_products)}个商品")
        return all_products

    async def execute(self):
        """
        异步执行闲鱼商品采集任务

        Returns:
            dict: 执行结果
        """
        self.execution_count += 1
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info("🚀 开始执行闲鱼商品采集任务")

        # 获取Cookie
        monitor = GoofishCookieMonitor()
        cookies_str = await monitor.get_cookies_string()

        if not cookies_str:
            logger.error("❌ 无法获取有效的Cookie")
            return {
                'status': 'error',
                'message': '无法获取有效的Cookie',
                'execution_time': current_time,
                'execution_count': self.execution_count,
            }

        logger.info("✅ Cookie获取成功")

        # 开始采集所有用户的商品
        all_products = []
        successful_users = 0
        failed_users = 0

        for user in self.users:
            user_id = user["user_id"]
            user_name = user["user_name"]

            try:
                # 获取用户的所有商品
                user_products = await self.get_all_user_products(user_id, user_name, cookies_str)

                if user_products:
                    all_products.extend(user_products)
                    successful_users += 1
                    logger.info(f"✅ 用户{user_name}({user_id})采集成功，获取{len(user_products)}个商品")
                else:
                    failed_users += 1
                    logger.warning(f"⚠️ 用户{user_name}({user_id})未获取到商品")

                # 用户之间的间隔，避免请求过于频繁
                if user != self.users[-1]:  # 不是最后一个用户
                    logger.info("⏳ 等待5秒后处理下一个用户...")
                    await asyncio.sleep(5)

            except Exception as e:
                failed_users += 1
                logger.error(f"❌ 用户{user_name}({user_id})采集失败: {e}")
                continue

        # 统计结果
        total_products = len(all_products)
        logger.info(f"🎉 商品采集任务完成！")
        logger.info(f"📊 统计结果:")
        logger.info(f"   - 成功用户: {successful_users}/{len(self.users)}")
        logger.info(f"   - 失败用户: {failed_users}/{len(self.users)}")
        logger.info(f"   - 总商品数: {total_products}")

        # 返回执行结果
        result = {
            'status': 'success',
            'message': '闲鱼商品采集任务执行完成',
            'execution_time': current_time,
            'execution_count': self.execution_count,
            'cookies_string': cookies_str,
            'total_users': len(self.users),
            'successful_users': successful_users,
            'failed_users': failed_users,
            'total_products': total_products,
            'sample_products': all_products[:5] if all_products else []  # 返回前5个商品作为示例
        }

        return result

    def on_success(self, result):
        """任务成功回调"""
        execution_count = result.get('execution_count', 0)
        total_products = result.get('total_products', 0)
        successful_users = result.get('successful_users', 0)
        logger.info(f"✅ 闲鱼商品采集任务 (第{execution_count}次) - 成功用户: {successful_users}, 总商品: {total_products}")

    def on_error(self, error):
        """任务失败回调"""
        logger.error(f"❌ 闲鱼商品采集任务: {error}")
