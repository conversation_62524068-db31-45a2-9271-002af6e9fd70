import asyncio
import random

import aiohttp
from datetime import datetime
from scheduler import BaseTask
import time
import json
from goofish_monitor import GoofishCookieMonitor
from loguru import logger

from utils.xianyu_utils import trans_cookies, generate_sign, generate_log_id
from models.xianyu_product import XianyuProduct
from config.database import get_database_config


class XianYuProDuctTask(BaseTask):
    """
    闲鱼商品采集任务
    """
    
    def __init__(self, task_id: str, db_config: dict = None):
        super().__init__(
            task_id=task_id,
            name="闲鱼商品采集任务",
            description="闲鱼商品采集任务"
        )
        self.execution_count = 0

        # 数据库配置
        if db_config:
            self.db_config = db_config
        else:
            # 使用项目配置文件中的数据库配置
            config = get_database_config()
            self.db_config = {
                'host': config['host'],
                'port': config['port'],
                'user': config['user'],
                'password': config['password'],
                'database': config['db']  # 注意这里是 'db' 不是 'database'
            }

        # 初始化数据库模型
        self.product_model = XianyuProduct(self.db_config)
        self.users = [
            {
                "user_id": "2201292691262",
                "user_name": "Auroral",
            },
            # {
            #     "user_id": "2216036737826",
            #     "user_name": "数据提取专家",
            # },
            # {
            #     "user_id": "3471902439",
            #     "user_name": "Esterketone",
            # },
            # {
            #     "user_id": "48549962",
            #     "user_name": "生活幻想家",
            # },
            # {
            #     "user_id": "2658142545",
            #     "user_name": "写代码赚猫粮",
            # },
        ]
        self.url = f"https://h5api.m.goofish.com/h5/mtop.idle.web.xyh.item.list/1.0/"
        # 设置通用headers
        self.headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'referer': 'https://www.goofish.com/',
            "content-type": "application/x-www-form-urlencoded",
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        }
    
    async def get_seller_products(self, seller_id: str, page_number: int, cookies_str: str, max_retry: int = 3):
        """
        获取卖家指定页面的商品列表
        
        Args:
            seller_id: 卖家用户ID
            page_number: 页码
            cookies_str: Cookie字符串
            max_retry: 最大重试次数
            
        Returns:
            dict: API响应数据，包含商品列表和分页信息
        """
        for attempt in range(max_retry):
            try:
                # 解析Cookie
                cookies_dict = trans_cookies(cookies_str) if cookies_str else {}
                if not cookies_dict.get('_m_h5_tk'):
                    logger.error("Cookie中缺少_m_h5_tk字段")
                    return None
                
                # 构建请求数据
                data_payload = {
                    "needGroupInfo": False,
                    "pageNumber": page_number,
                    "userId": seller_id,
                    "pageSize": 20,
                    "groupName": "在售",
                    "groupId": 46476081,
                    "defaultGroup": True
                }
                
                # 生成时间戳和签名
                timestamp = str(int(time.time() * 1000))
                data_val = json.dumps(data_payload)
                sign = generate_sign(timestamp, cookies_dict['_m_h5_tk'].split('_')[0], data_val)
                
                # 生成 log_id
                log_id = generate_log_id()
                
                # 构造 spm_pre 参数
                spm_pre_base = f"a21ybx.item.itemHeader.{random.randint(1, 10)}"
                spm_pre = f"{spm_pre_base}.{log_id}"
                
                # 构建请求参数
                params = {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "t": timestamp,
                    "sign": sign,
                    "v": "1.0",
                    "type": "originaljson",
                    "accountSite": "xianyu",
                    "dataType": "json",
                    "timeout": "20000",
                    "api": "mtop.idle.web.xyh.item.list",
                    "sessionOption": "AutoLoginOnly",
                    "spm_cnt": "a21ybx.item.0.0",
                    "spm_pre": spm_pre,
                    "log_id": log_id
                }
                
                # 构建请求体
                data = {"data": data_val}
                
                # 设置请求头
                headers = self.headers.copy()
                headers['cookie'] = cookies_str
                
                # 发起异步请求
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        self.url,
                        headers=headers,
                        params=params,
                        data=data,
                        timeout=30
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            
                            # 检查API响应状态
                            if result.get('ret') and result['ret'][0] == 'SUCCESS::调用成功':
                                logger.debug(f"成功获取卖家商品列表: {seller_id}, 页码: {page_number}")
                                return result
                            else:
                                logger.error(f"API调用失败: {result.get('ret', ['未知错误'])}")
                                return None
                        else:
                            logger.error(f"HTTP请求失败: {response.status}")
                            
            except Exception as e:
                logger.error(f"获取商品列表异常 (尝试 {attempt + 1}/{max_retry}): {seller_id}, 页码: {page_number} - {e}")
                if attempt < max_retry - 1:
                    await asyncio.sleep(1)  # 重试前等待1秒
                    continue
        
        logger.error(f"获取商品列表失败，已达到最大重试次数: {seller_id}, 页码: {page_number}")
        return None
    
    def _parse_product_from_card(self, card: dict, seller_id: str) -> dict:
        """
        从商品卡片数据中解析商品信息
        
        Args:
            card: 商品卡片数据
            seller_id: 卖家ID
            
        Returns:
            dict: 解析后的商品信息
        """
        try:
            card_data = card.get('cardData', {})
            
            # 基本商品信息
            product = {
                'item_id': card_data.get('id', ''),
                'seller_id': seller_id,
                'title': card_data.get('title', ''),
                'category_id': card_data.get('categoryId', ''),
                'auction_type': card_data.get('auctionType', ''),
                'item_status': card_data.get('itemStatus', 0),
                'detail_url': card_data.get('detailUrl', ''),
            }
            
            # 价格信息
            price_info = card_data.get('priceInfo', {})
            product.update({
                'price': price_info.get('price', ''),
                'price_pre_text': price_info.get('preText', ''),
            })
            
            # 图片信息
            pic_info = card_data.get('picInfo', {})
            product.update({
                'pic_url': pic_info.get('picUrl', ''),
                'pic_width': pic_info.get('width', 0),
                'pic_height': pic_info.get('height', 0),
                'has_video': pic_info.get('hasVideo', False),
            })
            
            # 详细参数
            detail_params = card_data.get('detailParams', {})
            product.update({
                'sold_price': detail_params.get('soldPrice', ''),
                'is_video': detail_params.get('isVideo', 'false'),
                'image_infos': detail_params.get('imageInfos', ''),
            })
            
            # 标签信息
            label_data_vo = card_data.get('itemLabelDataVO', {})
            label_data = label_data_vo.get('labelData', {})
            
            # 提取热销标签
            r2_tags = label_data.get('r2', {}).get('tagList', [])
            hot_sale_tag = ''
            if r2_tags:
                hot_sale_tag = r2_tags[0].get('data', {}).get('content', '')
            
            # 提取想要人数标签
            r3_tags = label_data.get('r3', {}).get('tagList', [])
            want_count_tag = ''
            if r3_tags:
                want_count_tag = r3_tags[0].get('data', {}).get('content', '')
            
            product.update({
                'hot_sale_tag': hot_sale_tag,
                'want_count_tag': want_count_tag,
                'label_bucket_id': label_data_vo.get('labelBucketId', ''),
            })
            
            # 追踪参数
            track_params = card_data.get('trackParams', {})
            product.update({
                'is_shop_user': track_params.get('isShopUser', ''),
                'label_bucket_id_track': track_params.get('labelBucketId', ''),
            })
            
            # 添加解析时间
            product['parsed_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            return product
            
        except Exception as e:
            logger.error(f"解析商品卡片数据失败: {e}")
            logger.debug(f"问题卡片数据: {card}")
            return None

    async def get_all_user_products(self, user_id: str, user_name: str, cookies_str: str,
                                   max_pages: int = 100, timeout_minutes: int = 10) -> list:
        """
        获取指定用户的所有商品（无商品数量限制）

        Args:
            user_id: 用户ID
            user_name: 用户名称
            cookies_str: Cookie字符串
            max_pages: 最大页数限制（防止无限循环）
            timeout_minutes: 超时限制（分钟）

        Returns:
            list: 用户的所有商品列表
        """
        all_products = []
        page_number = 1
        start_time = time.time()

        logger.info(f"开始获取用户所有商品: {user_id} ({user_name})")

        while True:
            # 超时检查
            if time.time() - start_time > timeout_minutes * 60:
                logger.warning(f"用户{user_id}商品采集超时({timeout_minutes}分钟)，停止采集")
                break

            # 页数限制检查
            if page_number > max_pages:
                logger.warning(f"用户{user_id}已达到最大页数限制: {max_pages}")
                break

            try:
                # 获取当前页商品
                response = await self.get_seller_products(user_id, page_number, cookies_str)
                if not response or not response.get('data'):
                    logger.warning(f"用户{user_id}获取第{page_number}页商品失败或无数据")
                    break

                data = response['data']
                card_list = data.get('cardList', [])

                if not card_list:
                    logger.info(f"用户{user_id}第{page_number}页无商品数据")
                    break

                # 解析当前页商品数据
                page_products = []
                for card in card_list:
                    product = self._parse_product_from_card(card, user_id)
                    if product:
                        page_products.append(product)

                all_products.extend(page_products)
                logger.info(f"用户{user_id}: 第{page_number}页获取到{len(page_products)}个商品，累计{len(all_products)}个")

                # 检查是否有下一页
                if not data.get('nextPage', False):
                    logger.info(f"用户{user_id}已获取所有商品页面")
                    break

                page_number += 1

                # 添加请求间隔，避免频繁调用
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"用户{user_id}获取第{page_number}页商品异常: {e}")
                break

        logger.info(f"用户{user_id} ({user_name}) 商品采集完成，共获取到{len(all_products)}个商品")
        return all_products

    async def get_product_detail(self, item_id: str, cookies_str: str, max_retry: int = 3):
        """
        获取商品详细信息

        Args:
            item_id: 商品ID
            cookies_str: Cookie字符串
            max_retry: 最大重试次数

        Returns:
            dict: 商品详情数据
        """
        for attempt in range(max_retry):
            try:
                # 解析Cookie
                cookies_dict = trans_cookies(cookies_str) if cookies_str else {}
                if not cookies_dict.get('_m_h5_tk'):
                    logger.error("Cookie中缺少_m_h5_tk字段")
                    return None

                # 构建请求数据
                data_payload = {"itemId": item_id}

                # 生成时间戳和签名
                timestamp = str(int(time.time() * 1000))
                data_val = json.dumps(data_payload)
                sign = generate_sign(timestamp, cookies_dict['_m_h5_tk'].split('_')[0], data_val)

                # 生成 log_id
                log_id = generate_log_id()

                # 构造 spm_pre 参数
                spm_pre_base = f"a21ybx.search.searchFeedList.{random.randint(1, 10)}"
                spm_pre = f"{spm_pre_base}.{log_id}"

                # 构建请求参数
                params = {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "t": timestamp,
                    "sign": sign,
                    "v": "1.0",
                    "type": "originaljson",
                    "accountSite": "xianyu",
                    "dataType": "json",
                    "timeout": "20000",
                    "api": "mtop.taobao.idle.pc.detail",
                    "sessionOption": "AutoLoginOnly",
                    "spm_cnt": "a21ybx.item.0.0",
                    "spm_pre": spm_pre,
                    "log_id": log_id
                }

                # 构建请求体
                data = {"data": data_val}

                # 设置请求头
                headers = self.headers.copy()
                headers['cookie'] = cookies_str

                # 商品详情API的URL
                detail_url = "https://h5api.m.goofish.com/h5/mtop.taobao.idle.pc.detail/1.0/"

                # 发起异步请求
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        detail_url,
                        headers=headers,
                        params=params,
                        data=data,
                        timeout=30
                    ) as response:
                        if response.status == 200:
                            result = await response.json()

                            # 检查API响应状态
                            if result.get('ret') and result['ret'][0] == 'SUCCESS::调用成功':
                                logger.debug(f"成功获取商品详情: {item_id}")
                                return result
                            else:
                                logger.error(f"商品详情API调用失败: {result.get('ret', ['未知错误'])}")
                                return None
                        else:
                            logger.error(f"商品详情HTTP请求失败: {response.status}")

            except Exception as e:
                logger.error(f"获取商品详情异常 (尝试 {attempt + 1}/{max_retry}): {item_id} - {e}")
                if attempt < max_retry - 1:
                    await asyncio.sleep(1)  # 重试前等待1秒
                    continue

        logger.error(f"获取商品详情失败，已达到最大重试次数: {item_id}")
        return None

    def _parse_product_detail(self, detail_response: dict, item_id: str) -> dict:
        """
        解析商品详情数据

        Args:
            detail_response: 详情API响应数据
            item_id: 商品ID

        Returns:
            dict: 解析后的详情信息
        """
        try:
            data = detail_response.get('data', {})
            item_do = data.get('itemDO', {})
            seller_do = data.get('sellerDO', {})

            # 商品详细信息
            detail_info = {
                'item_id': str(item_id),
                'detail_title': item_do.get('title', ''),
                'detail_desc': item_do.get('desc', ''),
                'rich_text_desc': item_do.get('richTextDesc', ''),
                'sold_price': item_do.get('soldPrice', ''),
                'original_price': item_do.get('originalPrice', ''),
                'transport_fee': item_do.get('transportFee', ''),
                'quantity': item_do.get('quantity', 0),
                'browse_cnt': item_do.get('browseCnt', 0),
                'want_cnt': item_do.get('wantCnt', 0),
                'favor_cnt': item_do.get('favorCnt', 0),
                'collect_cnt': item_do.get('collectCnt', 0),
                'item_status_str': item_do.get('itemStatusStr', ''),
                'gmt_create': item_do.get('gmtCreate', 0),
                'gmt_create_date': item_do.get('GMT_CREATE_DATE_KEY', ''),
            }

            # 分类信息
            item_cat_dto = item_do.get('itemCatDTO', {})
            detail_info.update({
                'cat_id': item_cat_dto.get('catId', 0),
                'channel_cat_id': item_cat_dto.get('channelCatId', 0),
                'root_channel_cat_id': item_cat_dto.get('rootChannelCatId', 0),
                'level2_channel_cat_id': item_cat_dto.get('level2ChannelCatId', 0),
                'level3_channel_cat_id': item_cat_dto.get('level3ChannelCatId', 0),
            })

            # 图片信息
            image_infos = item_do.get('imageInfos', [])
            detail_info.update({
                'image_count': len(image_infos),
                'main_image_url': '',
                'all_image_urls': [],
            })

            if image_infos:
                # 主图
                main_image = next((img for img in image_infos if img.get('major')), image_infos[0])
                detail_info['main_image_url'] = main_image.get('url', '')

                # 所有图片URL
                detail_info['all_image_urls'] = [img.get('url', '') for img in image_infos]

            # 卖家详细信息
            detail_info.update({
                'seller_id': seller_do.get('sellerId', ''),
                'seller_nick': seller_do.get('nick', ''),
                'seller_unique_name': seller_do.get('uniqueName', ''),
                'seller_signature': seller_do.get('signature', ''),
                'seller_city': seller_do.get('city', ''),
                'seller_portrait_url': seller_do.get('portraitUrl', ''),
                'seller_item_count': seller_do.get('itemCount', 0),
                'seller_has_sold_num': seller_do.get('hasSoldNumInteger', 0),
                'seller_user_reg_day': seller_do.get('userRegDay', 0),
                'seller_register_time': seller_do.get('registerTime', 0),
                'seller_last_visit_time': seller_do.get('lastVisitTime', ''),
                'seller_reply_ratio_24h': seller_do.get('replyRatio24h', ''),
                'seller_reply_interval': seller_do.get('replyInterval', ''),
                'seller_zhima_auth': seller_do.get('zhimaAuth', False),
            })

            # 卖家信用等级
            zhima_level_info = seller_do.get('zhimaLevelInfo', {})
            detail_info.update({
                'seller_zhima_level_code': zhima_level_info.get('levelCode', ''),
                'seller_zhima_level_name': zhima_level_info.get('levelName', ''),
            })

            # 卖家评价统计
            remark_do = seller_do.get('remarkDO', {})
            detail_info.update({
                'seller_good_remark_cnt': remark_do.get('sellerGoodRemarkCnt', 0),
                'seller_bad_remark_cnt': remark_do.get('sellerBadRemarkCnt', 0),
                'seller_default_remark_cnt': remark_do.get('sellerDefaultRemarkCnt', 0),
            })

            # 促销信息
            promotion_price_do = item_do.get('promotionPriceDO', {})
            detail_info.update({
                'promotion_name': promotion_price_do.get('promotionName', ''),
                'promotion_desc': promotion_price_do.get('promotionDesc', ''),
                'promotion_price_min': promotion_price_do.get('promotionPriceMin', ''),
                'min_discount_fee_str': promotion_price_do.get('minDiscountFeeStr', ''),
            })

            # 添加解析时间
            detail_info['detail_parsed_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            return detail_info

        except Exception as e:
            logger.error(f"解析商品详情数据失败: {e}")
            logger.debug(f"问题详情数据: {detail_response}")
            return None

    async def get_all_user_products_with_details(self, user_id: str, user_name: str, cookies_str: str,
                                               max_pages: int = 100, timeout_minutes: int = 15) -> list:
        """
        获取指定用户的所有商品及其详情信息

        Args:
            user_id: 用户ID
            user_name: 用户名称
            cookies_str: Cookie字符串
            max_pages: 最大页数限制
            timeout_minutes: 超时限制（分钟）

        Returns:
            list: 用户的所有商品列表（包含详情）
        """
        # 先获取商品列表
        all_products = await self.get_all_user_products(user_id, user_name, cookies_str, max_pages, timeout_minutes)

        if not all_products:
            return all_products

        logger.info(f"开始获取用户{user_id}的所有商品详情，共{len(all_products)}个商品")

        # 获取所有商品详情
        products_with_details = []
        detail_count = 0

        for i, product in enumerate(all_products, 1):

            item_id = product.get('item_id')
            if not item_id:
                products_with_details.append(product)
                continue

            try:
                # 获取商品详情
                detail_response = await self.get_product_detail(item_id, cookies_str)

                if detail_response:
                    # 解析详情数据
                    detail_info = self._parse_product_detail(detail_response, item_id)

                    if detail_info:
                        # 合并基本信息和详情信息
                        merged_product = {**product, **detail_info}
                        products_with_details.append(merged_product)
                        detail_count += 1
                        logger.info(f"用户{user_id}: 获取商品详情成功 {i}/{len(all_products)} - {product.get('title', '未知')}")
                    else:
                        # 详情解析失败，保留基本信息
                        products_with_details.append(product)
                        logger.warning(f"用户{user_id}: 商品详情解析失败 - {item_id}")
                else:
                    # 详情获取失败，保留基本信息
                    products_with_details.append(product)
                    logger.warning(f"用户{user_id}: 商品详情获取失败 - {item_id}")

                # 详情请求间隔，避免频繁调用
                await asyncio.sleep(3)

            except Exception as e:
                logger.error(f"用户{user_id}: 处理商品详情异常 - {item_id}: {e}")
                products_with_details.append(product)
                continue

        logger.info(f"用户{user_id} ({user_name}) 商品详情获取完成，成功获取{detail_count}/{len(all_products)}个详情")

        # 保存商品数据到数据库
        if products_with_details:
            logger.info(f"💾 开始保存用户{user_id}的商品数据到数据库...")
            save_result = await self.product_model.save_products_batch(products_with_details)
            logger.info(f"💾 用户{user_id}数据库保存完成: 成功{save_result['success']}个, 失败{save_result['failed']}个")

        return products_with_details

    async def execute(self):
        """
        异步执行闲鱼商品采集任务（获取所有商品及详情）

        Returns:
            dict: 执行结果
        """
        self.execution_count += 1
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info("🚀 开始执行闲鱼商品采集任务（包含所有商品详情）")

        # 获取Cookie
        monitor = GoofishCookieMonitor()
        cookies_str = await monitor.get_cookies_string()

        if not cookies_str:
            logger.error("❌ 无法获取有效的Cookie")
            return {
                'status': 'error',
                'message': '无法获取有效的Cookie',
                'execution_time': current_time,
                'execution_count': self.execution_count,
            }

        logger.info("✅ Cookie获取成功")

        # 开始采集所有用户的商品及详情
        all_products = []
        successful_users = 0
        failed_users = 0
        total_details_count = 0

        for user in self.users:
            user_id = user["user_id"]
            user_name = user["user_name"]

            try:
                # 获取用户的所有商品及详情
                user_products = await self.get_all_user_products_with_details(
                    user_id, user_name, cookies_str
                )

                if user_products:
                    all_products.extend(user_products)
                    successful_users += 1

                    # 统计详情数量
                    detail_count = sum(1 for p in user_products if p.get('detail_title'))
                    total_details_count += detail_count
                    logger.info(f"✅ 用户{user_name}({user_id})采集成功，获取{len(user_products)}个商品，{detail_count}个详情")
                else:
                    failed_users += 1
                    logger.warning(f"⚠️ 用户{user_name}({user_id})未获取到商品")

                # 用户之间的间隔，避免请求过于频繁
                if user != self.users[-1]:  # 不是最后一个用户
                    logger.info("⏳ 等待8秒后处理下一个用户...")
                    await asyncio.sleep(8)

            except Exception as e:
                failed_users += 1
                logger.error(f"❌ 用户{user_name}({user_id})采集失败: {e}")
                continue

        # 关闭数据库连接池
        try:
            await self.product_model.close_db_pool()
        except Exception as e:
            logger.warning(f"⚠️ 关闭数据库连接池异常: {e}")

        # 统计结果
        total_products = len(all_products)
        logger.info(f"🎉 商品采集任务完成！")
        logger.info(f"📊 统计结果:")
        logger.info(f"   - 成功用户: {successful_users}/{len(self.users)}")
        logger.info(f"   - 失败用户: {failed_users}/{len(self.users)}")
        logger.info(f"   - 总商品数: {total_products}")
        logger.info(f"   - 总详情数: {total_details_count}")

        # 返回执行结果
        result = {
            'status': 'success',
            'message': '闲鱼商品采集任务执行完成',
            'execution_time': current_time,
            'execution_count': self.execution_count,
            'cookies_string': cookies_str,
            'total_users': len(self.users),
            'successful_users': successful_users,
            'failed_users': failed_users,
            'total_products': total_products,
            'total_details': total_details_count,
            'sample_products': all_products[:3] if all_products else []  # 返回前3个商品作为示例
        }

        return result

    def on_success(self, result):
        """任务成功回调"""
        execution_count = result.get('execution_count', 0)
        total_products = result.get('total_products', 0)
        successful_users = result.get('successful_users', 0)
        logger.info(f"✅ 闲鱼商品采集任务 (第{execution_count}次) - 成功用户: {successful_users}, 总商品: {total_products}")

    def on_error(self, error):
        """任务失败回调"""
        logger.error(f"❌ 闲鱼商品采集任务: {error}")
