{"schedules": {"test_task": {"name": "测试任务", "description": "最简单的测试任务，只打印一条消息", "cron_expr": "0 * * * *", "cron_config": {"minute": 0}, "enabled": true, "created_at": "2025-07-30T21:49:17.220550", "last_run": "2025-07-30T22:00:17.317405", "next_run": null}, "xianyu_users_task": {"name": "闲鱼用户采集任务", "description": "闲鱼用户采集任务", "cron_expr": "0 * * * *", "cron_config": {"minute": 0}, "enabled": true, "created_at": "2025-07-30T20:18:10.110276", "last_run": "2025-07-30T20:18:10.112485", "next_run": null}, "xianyu_product_task": {"name": "闲鱼商品采集任务", "description": "闲鱼商品采集任务", "cron_expr": "0 * * * *", "cron_config": {"minute": 0}, "enabled": true, "created_at": "2025-07-30T21:49:17.220561", "last_run": "2025-07-30T22:00:17.318383", "next_run": null}}, "updated_at": "2025-07-30T22:00:24.037544"}